lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:
  .:
    dependencies:
      '@apollo/client':
        specifier: 3.9.9
        version: 3.9.9(@types/react@18.2.70)(graphql@16.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@graphql-codegen/near-operation-file-preset':
        specifier: 3.0.0
        version: 3.0.0(graphql@16.8.1)
      '@hookform/resolvers':
        specifier: 3.10.0
        version: 3.10.0(react-hook-form@7.51.1(react@18.2.0))
      '@radix-ui/react-accordion':
        specifier: 1.2.3
        version: 1.2.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-checkbox':
        specifier: 1.1.4
        version: 1.1.4(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-dialog':
        specifier: 1.1.4
        version: 1.1.4(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-dropdown-menu':
        specifier: 2.1.6
        version: 2.1.6(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-label':
        specifier: 2.1.1
        version: 2.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-popover':
        specifier: 1.1.4
        version: 1.1.4(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-radio-group':
        specifier: 1.2.3
        version: 1.2.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-select':
        specifier: 2.1.6
        version: 2.1.6(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-separator':
        specifier: 1.1.2
        version: 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-slider':
        specifier: 1.2.2
        version: 1.2.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-slot':
        specifier: 1.1.1
        version: 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-switch':
        specifier: 1.1.2
        version: 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-tooltip':
        specifier: 1.1.6
        version: 1.1.6(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@react-spring/types':
        specifier: 9.7.3
        version: 9.7.3
      '@rudderstack/analytics-js':
        specifier: 3.7.11
        version: 3.7.11
      '@sentry/cli':
        specifier: 2.30.2
        version: 2.30.2
      '@sentry/react':
        specifier: 7.108.0
        version: 7.108.0(react@18.2.0)
      '@sentry/vite-plugin':
        specifier: 2.16.1
        version: 2.16.1
      '@vitejs/plugin-react-swc':
        specifier: 3.7.2
        version: 3.7.2(vite@6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0))
      apollo-upload-client:
        specifier: 18.0.1
        version: 18.0.1(@apollo/client@3.9.9(@types/react@18.2.70)(graphql@16.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(graphql@16.8.1)
      class-variance-authority:
        specifier: 0.7.1
        version: 0.7.1
      clsx:
        specifier: 2.1.1
        version: 2.1.1
      date-fns:
        specifier: 3.6.0
        version: 3.6.0
      embla-carousel-autoplay:
        specifier: 8.5.2
        version: 8.5.2(embla-carousel@8.5.2)
      embla-carousel-fade:
        specifier: 8.5.2
        version: 8.5.2(embla-carousel@8.5.2)
      embla-carousel-react:
        specifier: 8.5.2
        version: 8.5.2(react@18.2.0)
      env-cmd:
        specifier: 10.1.0
        version: 10.1.0
      graphql:
        specifier: 16.8.1
        version: 16.8.1
      i18next:
        specifier: 23.10.1
        version: 23.10.1
      i18next-browser-languagedetector:
        specifier: 7.2.0
        version: 7.2.0
      i18next-locize-backend:
        specifier: 6.4.1
        version: 6.4.1
      lodash:
        specifier: 4.17.21
        version: 4.17.21
      lucide-react:
        specifier: 0.474.0
        version: 0.474.0(react@18.2.0)
      posthog-js:
        specifier: 1.249.3
        version: 1.249.3
      react:
        specifier: 18.2.0
        version: 18.2.0
      react-dom:
        specifier: 18.2.0
        version: 18.2.0(react@18.2.0)
      react-helmet-async:
        specifier: 2.0.5
        version: 2.0.5(react@18.2.0)
      react-hook-form:
        specifier: 7.51.1
        version: 7.51.1(react@18.2.0)
      react-i18next:
        specifier: 14.1.0
        version: 14.1.0(i18next@23.10.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-loading-skeleton:
        specifier: 3.4.0
        version: 3.4.0(react@18.2.0)
      react-number-format:
        specifier: 5.4.3
        version: 5.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-popper-tooltip:
        specifier: 4.4.2
        version: 4.4.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-router-dom:
        specifier: 6.22.3
        version: 6.22.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-toastify:
        specifier: 10.0.5
        version: 10.0.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-use:
        specifier: 17.5.1
        version: 17.5.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      sass:
        specifier: 1.72.0
        version: 1.72.0
      sonner:
        specifier: 2.0.1
        version: 2.0.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      tailwind-merge:
        specifier: 2.6.0
        version: 2.6.0
      tailwindcss-animate:
        specifier: 1.0.7
        version: 1.0.7(tailwindcss@3.4.17)
      uuid:
        specifier: 9.0.1
        version: 9.0.1
      vite:
        specifier: 6.0.11
        version: 6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0)
      vite-tsconfig-paths:
        specifier: 4.3.2
        version: 4.3.2(typescript@5.4.5)(vite@6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0))
      zod:
        specifier: 3.24.1
        version: 3.24.1
    devDependencies:
      '@commitlint/cli':
        specifier: 19.2.1
        version: 19.2.1(@types/node@20.11.30)(typescript@5.4.5)
      '@commitlint/config-conventional':
        specifier: 19.1.0
        version: 19.1.0
      '@eslint/eslintrc':
        specifier: ^3.2.0
        version: 3.3.1
      '@eslint/js':
        specifier: ^9.16.0
        version: 9.28.0
      '@graphql-codegen/cli':
        specifier: 5.0.2
        version: 5.0.2(@types/node@20.11.30)(graphql@16.8.1)(typescript@5.4.5)
      '@graphql-codegen/typescript-operations':
        specifier: 4.2.0
        version: 4.2.0(graphql@16.8.1)
      '@graphql-codegen/typescript-react-apollo':
        specifier: 4.3.0
        version: 4.3.0(graphql-tag@2.12.6(graphql@16.8.1))(graphql@16.8.1)
      '@savvywombat/tailwindcss-grid-areas':
        specifier: 4.0.0
        version: 4.0.0(tailwindcss@3.4.17)
      '@tanstack/eslint-plugin-query':
        specifier: ^5.72.2
        version: 5.78.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)
      '@types/apollo-upload-client':
        specifier: 18.0.0
        version: 18.0.0(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@types/jest':
        specifier: 29.5.12
        version: 29.5.12
      '@types/lodash':
        specifier: 4.17.15
        version: 4.17.15
      '@types/node':
        specifier: 20.11.30
        version: 20.11.30
      '@types/react':
        specifier: 18.2.70
        version: 18.2.70
      '@types/react-dom':
        specifier: 18.2.22
        version: 18.2.22
      '@types/uuid':
        specifier: 9.0.8
        version: 9.0.8
      '@typescript-eslint/eslint-plugin':
        specifier: ^8.17.0
        version: 8.34.0(@typescript-eslint/parser@8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5))(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)
      '@typescript-eslint/parser':
        specifier: ^8.17.0
        version: 8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)
      autoprefixer:
        specifier: 10.4.20
        version: 10.4.20(postcss@8.5.0)
      eslint:
        specifier: 9.16.0
        version: 9.16.0(jiti@2.4.2)
      eslint-config-prettier:
        specifier: ^9.1.0
        version: 9.1.0(eslint@9.16.0(jiti@2.4.2))
      eslint-plugin-prettier:
        specifier: ^5.2.1
        version: 5.4.1(eslint-config-prettier@9.1.0(eslint@9.16.0(jiti@2.4.2)))(eslint@9.16.0(jiti@2.4.2))(prettier@3.5.3)
      eslint-plugin-react:
        specifier: ^7.37.2
        version: 7.37.5(eslint@9.16.0(jiti@2.4.2))
      eslint-plugin-simple-import-sort:
        specifier: ^12.1.1
        version: 12.1.1(eslint@9.16.0(jiti@2.4.2))
      eslint-plugin-tailwindcss:
        specifier: ^3.18.0
        version: 3.18.0(tailwindcss@3.4.17)
      globals:
        specifier: ^15.13.0
        version: 15.15.0
      i:
        specifier: 0.3.7
        version: 0.3.7
      postcss:
        specifier: 8.5.0
        version: 8.5.0
      prettier:
        specifier: ^3.4.2
        version: 3.5.3
      tailwindcss:
        specifier: 3.4.17
        version: 3.4.17
      types:
        specifier: link:react-number-format/types/types
        version: link:react-number-format/types/types
      typescript:
        specifier: 5.4.5
        version: 5.4.5
      vite-plugin-svgr:
        specifier: 4.2.0
        version: 4.2.0(rollup@4.41.1)(typescript@5.4.5)(vite@6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0))
      vite-plugin-webfont-dl:
        specifier: 3.10.4
        version: 3.10.4(vite@6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0))

packages:
  '@alloc/quick-lru@5.2.0':
    resolution:
      {
        integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==,
        tarball: https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz,
      }
    engines: { node: '>=10' }

  '@ampproject/remapping@2.3.0':
    resolution:
      {
        integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==,
        tarball: https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz,
      }
    engines: { node: '>=6.0.0' }

  '@apollo/client@3.9.9':
    resolution:
      {
        integrity: sha512-/sMecU/M0WK9knrguts1lSLV8xFKzIgOMVb4mi6MOxgJXjliDB8PvOtmXhTqh2cVMMR4TzXgOnb+af/690zlQw==,
        tarball: https://registry.npmjs.org/@apollo/client/-/client-3.9.9.tgz,
      }
    peerDependencies:
      graphql: ^15.0.0 || ^16.0.0
      graphql-ws: ^5.5.5
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0
      subscriptions-transport-ws: ^0.9.0 || ^0.11.0
    peerDependenciesMeta:
      graphql-ws:
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
      subscriptions-transport-ws:
        optional: true

  '@ardatan/relay-compiler@12.0.0':
    resolution:
      {
        integrity: sha512-9anThAaj1dQr6IGmzBMcfzOQKTa5artjuPmw8NYK/fiGEMjADbSguBY2FMDykt+QhilR3wc9VA/3yVju7JHg7Q==,
        tarball: https://registry.npmjs.org/@ardatan/relay-compiler/-/relay-compiler-12.0.0.tgz,
      }
    hasBin: true
    peerDependencies:
      graphql: '*'

  '@ardatan/relay-compiler@12.0.3':
    resolution:
      {
        integrity: sha512-mBDFOGvAoVlWaWqs3hm1AciGHSQE1rqFc/liZTyYz/Oek9yZdT5H26pH2zAFuEiTiBVPPyMuqf5VjOFPI2DGsQ==,
        tarball: https://registry.npmjs.org/@ardatan/relay-compiler/-/relay-compiler-12.0.3.tgz,
      }
    hasBin: true
    peerDependencies:
      graphql: '*'

  '@babel/code-frame@7.27.1':
    resolution:
      {
        integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==,
        tarball: https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/compat-data@7.27.5':
    resolution:
      {
        integrity: sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==,
        tarball: https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/core@7.27.4':
    resolution:
      {
        integrity: sha512-bXYxrXFubeYdvB0NhD/NBB3Qi6aZeV20GOWVI47t2dkecCEoneR4NPVcb7abpXDEvejgrUfFtG6vG/zxAKmg+g==,
        tarball: https://registry.npmjs.org/@babel/core/-/core-7.27.4.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/generator@7.27.5':
    resolution:
      {
        integrity: sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==,
        tarball: https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-annotate-as-pure@7.27.3':
    resolution:
      {
        integrity: sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==,
        tarball: https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-compilation-targets@7.27.2':
    resolution:
      {
        integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==,
        tarball: https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-create-class-features-plugin@7.27.1':
    resolution:
      {
        integrity: sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==,
        tarball: https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-member-expression-to-functions@7.27.1':
    resolution:
      {
        integrity: sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==,
        tarball: https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-module-imports@7.27.1':
    resolution:
      {
        integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==,
        tarball: https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-module-transforms@7.27.3':
    resolution:
      {
        integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==,
        tarball: https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.27.1':
    resolution:
      {
        integrity: sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==,
        tarball: https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-plugin-utils@7.27.1':
    resolution:
      {
        integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==,
        tarball: https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-replace-supers@7.27.1':
    resolution:
      {
        integrity: sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==,
        tarball: https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    resolution:
      {
        integrity: sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==,
        tarball: https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-string-parser@7.27.1':
    resolution:
      {
        integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==,
        tarball: https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-validator-identifier@7.27.1':
    resolution:
      {
        integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==,
        tarball: https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helper-validator-option@7.27.1':
    resolution:
      {
        integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==,
        tarball: https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/helpers@7.27.6':
    resolution:
      {
        integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==,
        tarball: https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/parser@7.27.5':
    resolution:
      {
        integrity: sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==,
        tarball: https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz,
      }
    engines: { node: '>=6.0.0' }
    hasBin: true

  '@babel/plugin-proposal-class-properties@7.18.6':
    resolution:
      {
        integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==,
        tarball: https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz,
      }
    engines: { node: '>=6.9.0' }
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-object-rest-spread@7.20.7':
    resolution:
      {
        integrity: sha512-d2S98yCiLxDVmBmE8UjGcfPvNEUbA1U5q5WxaWFUGRzJSVAZqm5W6MbPct0jxnegUZ0niLeNX+IOzEs7wYg9Dg==,
        tarball: https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz,
      }
    engines: { node: '>=6.9.0' }
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-object-rest-spread instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution:
      {
        integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==,
        tarball: https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz,
      }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-flow@7.27.1':
    resolution:
      {
        integrity: sha512-p9OkPbZ5G7UT1MofwYFigGebnrzGJacoBSQM0/6bi/PUMVE+qlWDD/OalvQKbwgQzU6dl0xAv6r4X7Jme0RYxA==,
        tarball: https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.27.1':
    resolution:
      {
        integrity: sha512-UT/Jrhw57xg4ILHLFnzFpPDlMbcdEicaAtjPQpbj9wa8T4r5KVWCimHcL/460g8Ht0DMxDyjsLgiWSkVjnwPFg==,
        tarball: https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.27.1':
    resolution:
      {
        integrity: sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==,
        tarball: https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution:
      {
        integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==,
        tarball: https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz,
      }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-arrow-functions@7.27.1':
    resolution:
      {
        integrity: sha512-8Z4TGic6xW70FKThA5HYEKKyBpOOsucTOD1DjU3fZxDg+K3zBJcXMFnt/4yQiZnf5+MiOMSXQ9PaEK/Ilh1DeA==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.27.1':
    resolution:
      {
        integrity: sha512-cnqkuOtZLapWYZUYM5rVIdv1nXYuFVIltZ6ZJ7nIj585QsjKM5dhL2Fu/lICXZ1OyIAFc7Qy+bvDAtTXqGrlhg==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.27.5':
    resolution:
      {
        integrity: sha512-JF6uE2s67f0y2RZcm2kpAUEbD50vH62TyWVebxwHAlbSdM49VqPz8t4a1uIjp4NIOIZ4xzLfjY5emt/RCyC7TQ==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.5.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-classes@7.27.1':
    resolution:
      {
        integrity: sha512-7iLhfFAubmpeJe/Wo2TVuDrykh/zlWXLzPNdL0Jqn/Xu8R3QQ8h9ff8FQoISZOsw74/HFqFI7NX63HN7QFIHKA==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.27.1':
    resolution:
      {
        integrity: sha512-lj9PGWvMTVksbWiDT2tW68zGS/cyo4AkZ/QTp0sQT0mjPopCmrSkzxeXkznjqBxzDI6TclZhOJbBmbBLjuOZUw==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.27.3':
    resolution:
      {
        integrity: sha512-s4Jrok82JpiaIprtY2nHsYmrThKvvwgHwjgd7UMiYhZaN0asdXNLr0y+NjTfkA7SyQE5i2Fb7eawUOZmLvyqOA==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.27.3.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-flow-strip-types@7.27.1':
    resolution:
      {
        integrity: sha512-G5eDKsu50udECw7DL2AcsysXiQyB7Nfg521t2OAJ4tbfTJ27doHLeF/vlI1NZGlLdbb/v+ibvtL1YBQqYOwJGg==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.27.1':
    resolution:
      {
        integrity: sha512-BfbWFFEJFQzLCQ5N8VocnCtA8J1CLkNTe2Ms2wocj75dd6VpiqS5Z5quTYcUoo4Yq+DN0rtikODccuv7RU81sw==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.27.1':
    resolution:
      {
        integrity: sha512-1bQeydJF9Nr1eBCMMbC+hdwmRlsv5XYOMu03YSWFwNs0HsAmtSxxF1fyuYPqemVldVyFmlCU7w8UE14LupUSZQ==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.27.1':
    resolution:
      {
        integrity: sha512-0HCFSepIpLTkLcsi86GG3mTUzxV5jpmbv97hTETW3yzrAij8aqlD36toB1D0daVFJM8NK6GvKO0gslVQmm+zZA==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.27.1':
    resolution:
      {
        integrity: sha512-hqoBX4dcZ1I33jCSWcXrP+1Ku7kdqXf1oeah7ooKOIiAdKQ+uqftgCFNOSzA5AMS2XIHEYeGFg4cKRCdpxzVOQ==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.27.1':
    resolution:
      {
        integrity: sha512-OJguuwlTYlN0gBZFRPqwOGNWssZjfIUdS7HMYtN8c1KmwpwHFBwTeFZrg9XZa+DFTitWOW5iTAG7tyCUPsCCyw==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.27.1':
    resolution:
      {
        integrity: sha512-SFy8S9plRPbIcxlJ8A6mT/CxFdJx/c04JEctz4jf8YZaVS2px34j7NXRrlGlHkN/M2gnpL37ZpGRGVFLd3l8Ng==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.27.1':
    resolution:
      {
        integrity: sha512-018KRk76HWKeZ5l4oTj2zPpSh+NbGdt0st5S6x0pga6HgrjBOJb24mMDHorFopOOd6YHkLgOZ+zaCjZGPO4aKg==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.27.1':
    resolution:
      {
        integrity: sha512-oThy3BCuCha8kDZ8ZkgOg2exvPYUlprMukKQXI1r1pJ47NCvxfkEy8vK+r/hT9nF0Aa4H1WUPZZjHTFtAhGfmQ==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-display-name@7.27.1':
    resolution:
      {
        integrity: sha512-p9+Vl3yuHPmkirRrg021XiP+EETmPMQTLr6Ayjj85RLNEbb3Eya/4VI0vAdzQG9SEAl2Lnt7fy5lZyMzjYoZQQ==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx@7.27.1':
    resolution:
      {
        integrity: sha512-2KH4LWGSrJIkVf5tSiBFYuXDAoWRq2MMwgivCf+93dd0GQi8RXLjKA/0EvRnVV5G0hrHczsquXuD01L8s6dmBw==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.27.1':
    resolution:
      {
        integrity: sha512-N/wH1vcn4oYawbJ13Y/FxcQrWk63jhfNa7jef0ih7PHSIHX2LB7GWE1rkPrOnka9kwMxb6hMl19p7lidA+EHmQ==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.27.1':
    resolution:
      {
        integrity: sha512-kpb3HUqaILBJcRFVhFUs6Trdd4mkrzcGXss+6/mxUd273PfbWqSDHRzMT2234gIg2QYfAjvXLSquP1xECSg09Q==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.27.1':
    resolution:
      {
        integrity: sha512-fBJKiV7F2DxZUkg5EtHKXQdbsbURW3DZKQUWphDum0uRP6eHGGa/He9mc0mypL680pb+e/lDIthRohlv8NCHkg==,
        tarball: https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.27.1.tgz,
      }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.27.6':
    resolution:
      {
        integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==,
        tarball: https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/template@7.27.2':
    resolution:
      {
        integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==,
        tarball: https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/traverse@7.27.4':
    resolution:
      {
        integrity: sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==,
        tarball: https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@babel/types@7.27.6':
    resolution:
      {
        integrity: sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==,
        tarball: https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz,
      }
    engines: { node: '>=6.9.0' }

  '@commitlint/cli@19.2.1':
    resolution:
      {
        integrity: sha512-cbkYUJsLqRomccNxvoJTyv5yn0bSy05BBizVyIcLACkRbVUqYorC351Diw/XFSWC/GtpwiwT2eOvQgFZa374bg==,
        tarball: https://registry.npmjs.org/@commitlint/cli/-/cli-19.2.1.tgz,
      }
    engines: { node: '>=v18' }
    hasBin: true

  '@commitlint/config-conventional@19.1.0':
    resolution:
      {
        integrity: sha512-KIKD2xrp6Uuk+dcZVj3++MlzIr/Su6zLE8crEDQCZNvWHNQSeeGbzOlNtsR32TUy6H3JbP7nWgduAHCaiGQ6EA==,
        tarball: https://registry.npmjs.org/@commitlint/config-conventional/-/config-conventional-19.1.0.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/config-validator@19.8.1':
    resolution:
      {
        integrity: sha512-0jvJ4u+eqGPBIzzSdqKNX1rvdbSU1lPNYlfQQRIFnBgLy26BtC0cFnr7c/AyuzExMxWsMOte6MkTi9I3SQ3iGQ==,
        tarball: https://registry.npmjs.org/@commitlint/config-validator/-/config-validator-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/ensure@19.8.1':
    resolution:
      {
        integrity: sha512-mXDnlJdvDzSObafjYrOSvZBwkD01cqB4gbnnFuVyNpGUM5ijwU/r/6uqUmBXAAOKRfyEjpkGVZxaDsCVnHAgyw==,
        tarball: https://registry.npmjs.org/@commitlint/ensure/-/ensure-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/execute-rule@19.8.1':
    resolution:
      {
        integrity: sha512-YfJyIqIKWI64Mgvn/sE7FXvVMQER/Cd+s3hZke6cI1xgNT/f6ZAz5heND0QtffH+KbcqAwXDEE1/5niYayYaQA==,
        tarball: https://registry.npmjs.org/@commitlint/execute-rule/-/execute-rule-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/format@19.8.1':
    resolution:
      {
        integrity: sha512-kSJj34Rp10ItP+Eh9oCItiuN/HwGQMXBnIRk69jdOwEW9llW9FlyqcWYbHPSGofmjsqeoxa38UaEA5tsbm2JWw==,
        tarball: https://registry.npmjs.org/@commitlint/format/-/format-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/is-ignored@19.8.1':
    resolution:
      {
        integrity: sha512-AceOhEhekBUQ5dzrVhDDsbMaY5LqtN8s1mqSnT2Kz1ERvVZkNihrs3Sfk1Je/rxRNbXYFzKZSHaPsEJJDJV8dg==,
        tarball: https://registry.npmjs.org/@commitlint/is-ignored/-/is-ignored-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/lint@19.8.1':
    resolution:
      {
        integrity: sha512-52PFbsl+1EvMuokZXLRlOsdcLHf10isTPlWwoY1FQIidTsTvjKXVXYb7AvtpWkDzRO2ZsqIgPK7bI98x8LRUEw==,
        tarball: https://registry.npmjs.org/@commitlint/lint/-/lint-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/load@19.8.1':
    resolution:
      {
        integrity: sha512-9V99EKG3u7z+FEoe4ikgq7YGRCSukAcvmKQuTtUyiYPnOd9a2/H9Ak1J9nJA1HChRQp9OA/sIKPugGS+FK/k1A==,
        tarball: https://registry.npmjs.org/@commitlint/load/-/load-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/message@19.8.1':
    resolution:
      {
        integrity: sha512-+PMLQvjRXiU+Ae0Wc+p99EoGEutzSXFVwQfa3jRNUZLNW5odZAyseb92OSBTKCu+9gGZiJASt76Cj3dLTtcTdg==,
        tarball: https://registry.npmjs.org/@commitlint/message/-/message-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/parse@19.8.1':
    resolution:
      {
        integrity: sha512-mmAHYcMBmAgJDKWdkjIGq50X4yB0pSGpxyOODwYmoexxxiUCy5JJT99t1+PEMK7KtsCtzuWYIAXYAiKR+k+/Jw==,
        tarball: https://registry.npmjs.org/@commitlint/parse/-/parse-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/read@19.8.1':
    resolution:
      {
        integrity: sha512-03Jbjb1MqluaVXKHKRuGhcKWtSgh3Jizqy2lJCRbRrnWpcM06MYm8th59Xcns8EqBYvo0Xqb+2DoZFlga97uXQ==,
        tarball: https://registry.npmjs.org/@commitlint/read/-/read-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/resolve-extends@19.8.1':
    resolution:
      {
        integrity: sha512-GM0mAhFk49I+T/5UCYns5ayGStkTt4XFFrjjf0L4S26xoMTSkdCf9ZRO8en1kuopC4isDFuEm7ZOm/WRVeElVg==,
        tarball: https://registry.npmjs.org/@commitlint/resolve-extends/-/resolve-extends-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/rules@19.8.1':
    resolution:
      {
        integrity: sha512-Hnlhd9DyvGiGwjfjfToMi1dsnw1EXKGJNLTcsuGORHz6SS9swRgkBsou33MQ2n51/boIDrbsg4tIBbRpEWK2kw==,
        tarball: https://registry.npmjs.org/@commitlint/rules/-/rules-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/to-lines@19.8.1':
    resolution:
      {
        integrity: sha512-98Mm5inzbWTKuZQr2aW4SReY6WUukdWXuZhrqf1QdKPZBCCsXuG87c+iP0bwtD6DBnmVVQjgp4whoHRVixyPBg==,
        tarball: https://registry.npmjs.org/@commitlint/to-lines/-/to-lines-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/top-level@19.8.1':
    resolution:
      {
        integrity: sha512-Ph8IN1IOHPSDhURCSXBz44+CIu+60duFwRsg6HqaISFHQHbmBtxVw4ZrFNIYUzEP7WwrNPxa2/5qJ//NK1FGcw==,
        tarball: https://registry.npmjs.org/@commitlint/top-level/-/top-level-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@commitlint/types@19.8.1':
    resolution:
      {
        integrity: sha512-/yCrWGCoA1SVKOks25EGadP9Pnj0oAIHGpl2wH2M2Y46dPM2ueb8wyCVOD7O3WCTkaJ0IkKvzhl1JY7+uCT2Dw==,
        tarball: https://registry.npmjs.org/@commitlint/types/-/types-19.8.1.tgz,
      }
    engines: { node: '>=v18' }

  '@envelop/core@5.2.3':
    resolution:
      {
        integrity: sha512-KfoGlYD/XXQSc3BkM1/k15+JQbkQ4ateHazeZoWl9P71FsLTDXSjGy6j7QqfhpIDSbxNISqhPMfZHYSbDFOofQ==,
        tarball: https://registry.npmjs.org/@envelop/core/-/core-5.2.3.tgz,
      }
    engines: { node: '>=18.0.0' }

  '@envelop/instrumentation@1.0.0':
    resolution:
      {
        integrity: sha512-cxgkB66RQB95H3X27jlnxCRNTmPuSTgmBAq6/4n2Dtv4hsk4yz8FadA1ggmd0uZzvKqWD6CR+WFgTjhDqg7eyw==,
        tarball: https://registry.npmjs.org/@envelop/instrumentation/-/instrumentation-1.0.0.tgz,
      }
    engines: { node: '>=18.0.0' }

  '@envelop/types@5.2.1':
    resolution:
      {
        integrity: sha512-CsFmA3u3c2QoLDTfEpGr4t25fjMU31nyvse7IzWTvb0ZycuPjMjb0fjlheh+PbhBYb9YLugnT2uY6Mwcg1o+Zg==,
        tarball: https://registry.npmjs.org/@envelop/types/-/types-5.2.1.tgz,
      }
    engines: { node: '>=18.0.0' }

  '@esbuild/aix-ppc64@0.24.2':
    resolution:
      {
        integrity: sha512-thpVCb/rhxE/BnMLQ7GReQLLN8q9qbHmI55F4489/ByVg2aQaQ6kbcLb6FHkocZzQhxc4gx0sCk0tJkKBFzDhA==,
        tarball: https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.24.2':
    resolution:
      {
        integrity: sha512-cNLgeqCqV8WxfcTIOeL4OAtSmL8JjcN6m09XIgro1Wi7cF4t/THaWEa7eL5CMoMBdjoHOTh/vwTO/o2TRXIyzg==,
        tarball: https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.24.2':
    resolution:
      {
        integrity: sha512-tmwl4hJkCfNHwFB3nBa8z1Uy3ypZpxqxfTQOcHX+xRByyYgunVbZ9MzUUfb0RxaHIMnbHagwAxuTL+tnNM+1/Q==,
        tarball: https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.24.2':
    resolution:
      {
        integrity: sha512-B6Q0YQDqMx9D7rvIcsXfmJfvUYLoP722bgfBlO5cGvNVb5V/+Y7nhBE3mHV9OpxBf4eAS2S68KZztiPaWq4XYw==,
        tarball: https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.24.2':
    resolution:
      {
        integrity: sha512-kj3AnYWc+CekmZnS5IPu9D+HWtUI49hbnyqk0FLEJDbzCIQt7hg7ucF1SQAilhtYpIujfaHr6O0UHlzzSPdOeA==,
        tarball: https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.24.2':
    resolution:
      {
        integrity: sha512-WeSrmwwHaPkNR5H3yYfowhZcbriGqooyu3zI/3GGpF8AyUdsrrP0X6KumITGA9WOyiJavnGZUwPGvxvwfWPHIA==,
        tarball: https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.24.2':
    resolution:
      {
        integrity: sha512-UN8HXjtJ0k/Mj6a9+5u6+2eZ2ERD7Edt1Q9IZiB5UZAIdPnVKDoG7mdTVGhHJIeEml60JteamR3qhsr1r8gXvg==,
        tarball: https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.24.2':
    resolution:
      {
        integrity: sha512-TvW7wE/89PYW+IevEJXZ5sF6gJRDY/14hyIGFXdIucxCsbRmLUcjseQu1SyTko+2idmCw94TgyaEZi9HUSOe3Q==,
        tarball: https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.24.2':
    resolution:
      {
        integrity: sha512-7HnAD6074BW43YvvUmE/35Id9/NB7BeX5EoNkK9obndmZBUk8xmJJeU7DwmUeN7tkysslb2eSl6CTrYz6oEMQg==,
        tarball: https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.24.2':
    resolution:
      {
        integrity: sha512-n0WRM/gWIdU29J57hJyUdIsk0WarGd6To0s+Y+LwvlC55wt+GT/OgkwoXCXvIue1i1sSNWblHEig00GBWiJgfA==,
        tarball: https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.24.2':
    resolution:
      {
        integrity: sha512-sfv0tGPQhcZOgTKO3oBE9xpHuUqguHvSo4jl+wjnKwFpapx+vUDcawbwPNuBIAYdRAvIDBfZVvXprIj3HA+Ugw==,
        tarball: https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.24.2':
    resolution:
      {
        integrity: sha512-CN9AZr8kEndGooS35ntToZLTQLHEjtVB5n7dl8ZcTZMonJ7CCfStrYhrzF97eAecqVbVJ7APOEe18RPI4KLhwQ==,
        tarball: https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.24.2':
    resolution:
      {
        integrity: sha512-iMkk7qr/wl3exJATwkISxI7kTcmHKE+BlymIAbHO8xanq/TjHaaVThFF6ipWzPHryoFsesNQJPE/3wFJw4+huw==,
        tarball: https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.24.2':
    resolution:
      {
        integrity: sha512-shsVrgCZ57Vr2L8mm39kO5PPIb+843FStGt7sGGoqiiWYconSxwTiuswC1VJZLCjNiMLAMh34jg4VSEQb+iEbw==,
        tarball: https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.24.2':
    resolution:
      {
        integrity: sha512-4eSFWnU9Hhd68fW16GD0TINewo1L6dRrB+oLNNbYyMUAeOD2yCK5KXGK1GH4qD/kT+bTEXjsyTCiJGHPZ3eM9Q==,
        tarball: https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.24.2':
    resolution:
      {
        integrity: sha512-S0Bh0A53b0YHL2XEXC20bHLuGMOhFDO6GN4b3YjRLK//Ep3ql3erpNcPlEFed93hsQAjAQDNsvcK+hV90FubSw==,
        tarball: https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.24.2':
    resolution:
      {
        integrity: sha512-8Qi4nQcCTbLnK9WoMjdC9NiTG6/E38RNICU6sUNqK0QFxCYgoARqVqxdFmWkdonVsvGqWhmm7MO0jyTqLqwj0Q==,
        tarball: https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.24.2':
    resolution:
      {
        integrity: sha512-wuLK/VztRRpMt9zyHSazyCVdCXlpHkKm34WUyinD2lzK07FAHTq0KQvZZlXikNWkDGoT6x3TD51jKQ7gMVpopw==,
        tarball: https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.24.2':
    resolution:
      {
        integrity: sha512-VefFaQUc4FMmJuAxmIHgUmfNiLXY438XrL4GDNV1Y1H/RW3qow68xTwjZKfj/+Plp9NANmzbH5R40Meudu8mmw==,
        tarball: https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.24.2':
    resolution:
      {
        integrity: sha512-YQbi46SBct6iKnszhSvdluqDmxCJA+Pu280Av9WICNwQmMxV7nLRHZfjQzwbPs3jeWnuAhE9Jy0NrnJ12Oz+0A==,
        tarball: https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.24.2':
    resolution:
      {
        integrity: sha512-+iDS6zpNM6EnJyWv0bMGLWSWeXGN/HTaF/LXHXHwejGsVi+ooqDfMCCTerNFxEkM3wYVcExkeGXNqshc9iMaOA==,
        tarball: https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.24.2':
    resolution:
      {
        integrity: sha512-hTdsW27jcktEvpwNHJU4ZwWFGkz2zRJUz8pvddmXPtXDzVKTTINmlmga3ZzwcuMpUvLw7JkLy9QLKyGpD2Yxig==,
        tarball: https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.24.2':
    resolution:
      {
        integrity: sha512-LihEQ2BBKVFLOC9ZItT9iFprsE9tqjDjnbulhHoFxYQtQfai7qfluVODIYxt1PgdoyQkz23+01rzwNwYfutxUQ==,
        tarball: https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.24.2':
    resolution:
      {
        integrity: sha512-q+iGUwfs8tncmFC9pcnD5IvRHAzmbwQ3GPS5/ceCyHdjXubwQWI12MKWSNSMYLJMq23/IUCvJMS76PDqXe1fxA==,
        tarball: https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.24.2':
    resolution:
      {
        integrity: sha512-7VTgWzgMGvup6aSqDPLiW5zHaxYJGTO4OokMjIlrCtf+VpEL+cXKtCvg723iguPYI5oaUNdS+/V7OU2gvXVWEg==,
        tarball: https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.7.0':
    resolution:
      {
        integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==,
        tarball: https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution:
      {
        integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==,
        tarball: https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz,
      }
    engines: { node: ^12.0.0 || ^14.0.0 || >=16.0.0 }

  '@eslint/config-array@0.19.2':
    resolution:
      {
        integrity: sha512-GNKqxfHG2ySmJOBSHg7LxeUx4xpuCoFjacmlCoYWEbaPXLwvfIjixRI12xCQZeULksQb23uiA8F40w5TojpV7w==,
        tarball: https://registry.npmjs.org/@eslint/config-array/-/config-array-0.19.2.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@eslint/core@0.13.0':
    resolution:
      {
        integrity: sha512-yfkgDw1KR66rkT5A8ci4irzDysN7FRpq3ttJolR88OqQikAWqwA8j5VZyas+vjyBNFIJ7MfybJ9plMILI2UrCw==,
        tarball: https://registry.npmjs.org/@eslint/core/-/core-0.13.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@eslint/core@0.9.1':
    resolution:
      {
        integrity: sha512-GuUdqkyyzQI5RMIWkHhvTWLCyLo1jNK3vzkSyaExH5kHPDHcuL2VOpHjmMY+y3+NC69qAKToBqldTBgYeLSr9Q==,
        tarball: https://registry.npmjs.org/@eslint/core/-/core-0.9.1.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@eslint/eslintrc@3.3.1':
    resolution:
      {
        integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==,
        tarball: https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.3.1.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@eslint/js@9.16.0':
    resolution:
      {
        integrity: sha512-tw2HxzQkrbeuvyj1tG2Yqq+0H9wGoI2IMk4EOsQeX+vmd75FtJAzf+gTA69WF+baUKRYQ3x2kbLE08js5OsTVg==,
        tarball: https://registry.npmjs.org/@eslint/js/-/js-9.16.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@eslint/js@9.28.0':
    resolution:
      {
        integrity: sha512-fnqSjGWd/CoIp4EXIxWVK/sHA6DOHN4+8Ix2cX5ycOY7LG0UY8nHCU5pIp2eaE1Mc7Qd8kHspYNzYXT2ojPLzg==,
        tarball: https://registry.npmjs.org/@eslint/js/-/js-9.28.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@eslint/object-schema@2.1.6':
    resolution:
      {
        integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==,
        tarball: https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.6.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@eslint/plugin-kit@0.2.8':
    resolution:
      {
        integrity: sha512-ZAoA40rNMPwSm+AeHpCq8STiNAwzWLJuP8Xv4CHIc9wv/PSuExjMrmjfYNj682vW0OOiZ1HKxzvjQr9XZIisQA==,
        tarball: https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.8.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@fastify/busboy@3.1.1':
    resolution:
      {
        integrity: sha512-5DGmA8FTdB2XbDeEwc/5ZXBl6UbBAyBOOLlPuBnZ/N1SwdH9Ii+cOX3tBROlDgcTXxjOYnLMVoKk9+FXAw0CJw==,
        tarball: https://registry.npmjs.org/@fastify/busboy/-/busboy-3.1.1.tgz,
      }

  '@floating-ui/core@1.7.1':
    resolution:
      {
        integrity: sha512-azI0DrjMMfIug/ExbBaeDVJXcY0a7EPvPjb2xAJPa4HeimBX+Z18HK8QQR3jb6356SnDDdxx+hinMLcJEDdOjw==,
        tarball: https://registry.npmjs.org/@floating-ui/core/-/core-1.7.1.tgz,
      }

  '@floating-ui/dom@1.7.1':
    resolution:
      {
        integrity: sha512-cwsmW/zyw5ltYTUeeYJ60CnQuPqmGwuGVhG9w0PRaRKkAyi38BT5CKrpIbb+jtahSwUl04cWzSx9ZOIxeS6RsQ==,
        tarball: https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.1.tgz,
      }

  '@floating-ui/react-dom@2.1.3':
    resolution:
      {
        integrity: sha512-huMBfiU9UnQ2oBwIhgzyIiSpVgvlDstU8CX0AF+wS+KzmYMs0J2a3GwuFHV1Lz+jlrQGeC1fF+Nv0QoumyV0bA==,
        tarball: https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.3.tgz,
      }
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/utils@0.2.9':
    resolution:
      {
        integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==,
        tarball: https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.9.tgz,
      }

  '@graphql-codegen/add@3.2.3':
    resolution:
      {
        integrity: sha512-sQOnWpMko4JLeykwyjFTxnhqjd/3NOG2OyMuvK76Wnnwh8DRrNf2VEs2kmSvLl7MndMlOj7Kh5U154dVcvhmKQ==,
        tarball: https://registry.npmjs.org/@graphql-codegen/add/-/add-3.2.3.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/add@5.0.3':
    resolution:
      {
        integrity: sha512-SxXPmramkth8XtBlAHu4H4jYcYXM/o3p01+psU+0NADQowA8jtYkK6MW5rV6T+CxkEaNZItfSmZRPgIuypcqnA==,
        tarball: https://registry.npmjs.org/@graphql-codegen/add/-/add-5.0.3.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/cli@5.0.2':
    resolution:
      {
        integrity: sha512-MBIaFqDiLKuO4ojN6xxG9/xL9wmfD3ZjZ7RsPjwQnSHBCUXnEkdKvX+JVpx87Pq29Ycn8wTJUguXnTZ7Di0Mlw==,
        tarball: https://registry.npmjs.org/@graphql-codegen/cli/-/cli-5.0.2.tgz,
      }
    hasBin: true
    peerDependencies:
      '@parcel/watcher': ^2.1.0
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
    peerDependenciesMeta:
      '@parcel/watcher':
        optional: true

  '@graphql-codegen/client-preset@4.8.1':
    resolution:
      {
        integrity: sha512-XLF2V7WKLnepvrGE44JP+AvjS+Oz9AT0oYgTl/6d9btQ+2VYFcmwQPjNAuMVHipqE9I6h8hSEfH9hUrzUptB1g==,
        tarball: https://registry.npmjs.org/@graphql-codegen/client-preset/-/client-preset-4.8.1.tgz,
      }
    engines: { node: '>=16' }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
      graphql-sock: ^1.0.0
    peerDependenciesMeta:
      graphql-sock:
        optional: true

  '@graphql-codegen/core@4.0.2':
    resolution:
      {
        integrity: sha512-IZbpkhwVqgizcjNiaVzNAzm/xbWT6YnGgeOLwVjm4KbJn3V2jchVtuzHH09G5/WkkLSk2wgbXNdwjM41JxO6Eg==,
        tarball: https://registry.npmjs.org/@graphql-codegen/core/-/core-4.0.2.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/gql-tag-operations@4.0.17':
    resolution:
      {
        integrity: sha512-2pnvPdIG6W9OuxkrEZ6hvZd142+O3B13lvhrZ48yyEBh2ujtmKokw0eTwDHtlXUqjVS0I3q7+HB2y12G/m69CA==,
        tarball: https://registry.npmjs.org/@graphql-codegen/gql-tag-operations/-/gql-tag-operations-4.0.17.tgz,
      }
    engines: { node: '>=16' }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/near-operation-file-preset@3.0.0':
    resolution:
      {
        integrity: sha512-HRPaa7OsIAHQBFeGiTUVdjFcxzgvAs7uxSqcLEJgDpCr9cffpwnlgWP3gK79KnTiHsRkyb55U1K4YyrL00g1Cw==,
        tarball: https://registry.npmjs.org/@graphql-codegen/near-operation-file-preset/-/near-operation-file-preset-3.0.0.tgz,
      }
    engines: { node: '>= 16.0.0' }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/plugin-helpers@2.7.2':
    resolution:
      {
        integrity: sha512-kln2AZ12uii6U59OQXdjLk5nOlh1pHis1R98cDZGFnfaiAbX9V3fxcZ1MMJkB7qFUymTALzyjZoXXdyVmPMfRg==,
        tarball: https://registry.npmjs.org/@graphql-codegen/plugin-helpers/-/plugin-helpers-2.7.2.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/plugin-helpers@3.1.2':
    resolution:
      {
        integrity: sha512-emOQiHyIliVOIjKVKdsI5MXj312zmRDwmHpyUTZMjfpvxq/UVAHUJIVdVf+lnjjrI+LXBTgMlTWTgHQfmICxjg==,
        tarball: https://registry.npmjs.org/@graphql-codegen/plugin-helpers/-/plugin-helpers-3.1.2.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/plugin-helpers@5.1.0':
    resolution:
      {
        integrity: sha512-Y7cwEAkprbTKzVIe436TIw4w03jorsMruvCvu0HJkavaKMQbWY+lQ1RIuROgszDbxAyM35twB5/sUvYG5oW+yg==,
        tarball: https://registry.npmjs.org/@graphql-codegen/plugin-helpers/-/plugin-helpers-5.1.0.tgz,
      }
    engines: { node: '>=16' }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/schema-ast@4.1.0':
    resolution:
      {
        integrity: sha512-kZVn0z+th9SvqxfKYgztA6PM7mhnSZaj4fiuBWvMTqA+QqQ9BBed6Pz41KuD/jr0gJtnlr2A4++/0VlpVbCTmQ==,
        tarball: https://registry.npmjs.org/@graphql-codegen/schema-ast/-/schema-ast-4.1.0.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/typed-document-node@5.1.1':
    resolution:
      {
        integrity: sha512-Bp/BrMZDKRwzuVeLv+pSljneqONM7gqu57ZaV34Jbncu2hZWMRDMfizTKghoEwwZbRCYYfJO9tA0sYVVIfI1kg==,
        tarball: https://registry.npmjs.org/@graphql-codegen/typed-document-node/-/typed-document-node-5.1.1.tgz,
      }
    engines: { node: '>=16' }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/typescript-operations@4.2.0':
    resolution:
      {
        integrity: sha512-lmuwYb03XC7LNRS8oo9M4/vlOrq/wOKmTLBHlltK2YJ1BO/4K/Q9Jdv/jDmJpNydHVR1fmeF4wAfsIp1f9JibA==,
        tarball: https://registry.npmjs.org/@graphql-codegen/typescript-operations/-/typescript-operations-4.2.0.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/typescript-operations@4.6.1':
    resolution:
      {
        integrity: sha512-k92laxhih7s0WZ8j5WMIbgKwhe64C0As6x+PdcvgZFMudDJ7rPJ/hFqJ9DCRxNjXoHmSjnr6VUuQZq4lT1RzCA==,
        tarball: https://registry.npmjs.org/@graphql-codegen/typescript-operations/-/typescript-operations-4.6.1.tgz,
      }
    engines: { node: '>=16' }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
      graphql-sock: ^1.0.0
    peerDependenciesMeta:
      graphql-sock:
        optional: true

  '@graphql-codegen/typescript-react-apollo@4.3.0':
    resolution:
      {
        integrity: sha512-h+IxCGrOTDD60/6ztYDQs81yKDZZq/8aHqM9HHrZ9FiZn145O48VnQNCmGm88I619G9rEET8cCOrtYkCt+ZSzA==,
        tarball: https://registry.npmjs.org/@graphql-codegen/typescript-react-apollo/-/typescript-react-apollo-4.3.0.tgz,
      }
    engines: { node: '>= 16.0.0' }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
      graphql-tag: ^2.0.0

  '@graphql-codegen/typescript@4.1.6':
    resolution:
      {
        integrity: sha512-vpw3sfwf9A7S+kIUjyFxuvrywGxd4lmwmyYnnDVjVE4kSQ6Td3DpqaPTy8aNQ6O96vFoi/bxbZS2BW49PwSUUA==,
        tarball: https://registry.npmjs.org/@graphql-codegen/typescript/-/typescript-4.1.6.tgz,
      }
    engines: { node: '>=16' }
    peerDependencies:
      graphql: ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/visitor-plugin-common@2.13.1':
    resolution:
      {
        integrity: sha512-mD9ufZhDGhyrSaWQGrU1Q1c5f01TeWtSWy/cDwXYjJcHIj1Y/DG2x0tOflEfCvh5WcnmHNIw4lzDsg1W7iFJEg==,
        tarball: https://registry.npmjs.org/@graphql-codegen/visitor-plugin-common/-/visitor-plugin-common-2.13.1.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/visitor-plugin-common@5.1.0':
    resolution:
      {
        integrity: sha512-eamQxtA9bjJqI2lU5eYoA1GbdMIRT2X8m8vhWYsVQVWD3qM7sx/IqJU0kx0J3Vd4/CSd36BzL6RKwksibytDIg==,
        tarball: https://registry.npmjs.org/@graphql-codegen/visitor-plugin-common/-/visitor-plugin-common-5.1.0.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-codegen/visitor-plugin-common@5.8.0':
    resolution:
      {
        integrity: sha512-lC1E1Kmuzi3WZUlYlqB4fP6+CvbKH9J+haU1iWmgsBx5/sO2ROeXJG4Dmt8gP03bI2BwjiwV5WxCEMlyeuzLnA==,
        tarball: https://registry.npmjs.org/@graphql-codegen/visitor-plugin-common/-/visitor-plugin-common-5.8.0.tgz,
      }
    engines: { node: '>=16' }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  '@graphql-hive/signal@1.0.0':
    resolution:
      {
        integrity: sha512-RiwLMc89lTjvyLEivZ/qxAC5nBHoS2CtsWFSOsN35sxG9zoo5Z+JsFHM8MlvmO9yt+MJNIyC5MLE1rsbOphlag==,
        tarball: https://registry.npmjs.org/@graphql-hive/signal/-/signal-1.0.0.tgz,
      }
    engines: { node: '>=18.0.0' }

  '@graphql-tools/apollo-engine-loader@8.0.20':
    resolution:
      {
        integrity: sha512-m5k9nXSyjq31yNsEqDXLyykEjjn3K3Mo73oOKI+Xjy8cpnsgbT4myeUJIYYQdLrp7fr9Y9p7ZgwT5YcnwmnAbA==,
        tarball: https://registry.npmjs.org/@graphql-tools/apollo-engine-loader/-/apollo-engine-loader-8.0.20.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/batch-execute@9.0.17':
    resolution:
      {
        integrity: sha512-i7BqBkUP2+ex8zrQrCQTEt6nYHQmIey9qg7CMRRa1hXCY2X8ZCVjxsvbsi7gOLwyI/R3NHxSRDxmzZevE2cPLg==,
        tarball: https://registry.npmjs.org/@graphql-tools/batch-execute/-/batch-execute-9.0.17.tgz,
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/code-file-loader@8.1.20':
    resolution:
      {
        integrity: sha512-GzIbjjWJIc04KWnEr8VKuPe0FA2vDTlkaeub5p4lLimljnJ6C0QSkOyCUnFmsB9jetQcHm0Wfmn/akMnFUG+wA==,
        tarball: https://registry.npmjs.org/@graphql-tools/code-file-loader/-/code-file-loader-8.1.20.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/delegate@10.2.19':
    resolution:
      {
        integrity: sha512-aaCGAALTQmKctHwumbtz0c5XehGjYLSfoDx1IB2vdPt76Q0MKz2AiEDlENgzTVr4JHH7fd9YNrd+IO3D8tFlIg==,
        tarball: https://registry.npmjs.org/@graphql-tools/delegate/-/delegate-10.2.19.tgz,
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/documents@1.0.1':
    resolution:
      {
        integrity: sha512-aweoMH15wNJ8g7b2r4C4WRuJxZ0ca8HtNO54rkye/3duxTkW4fGBEutCx03jCIr5+a1l+4vFJNP859QnAVBVCA==,
        tarball: https://registry.npmjs.org/@graphql-tools/documents/-/documents-1.0.1.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/executor-common@0.0.4':
    resolution:
      {
        integrity: sha512-SEH/OWR+sHbknqZyROCFHcRrbZeUAyjCsgpVWCRjqjqRbiJiXq6TxNIIOmpXgkrXWW/2Ev4Wms6YSGJXjdCs6Q==,
        tarball: https://registry.npmjs.org/@graphql-tools/executor-common/-/executor-common-0.0.4.tgz,
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/executor-graphql-ws@2.0.5':
    resolution:
      {
        integrity: sha512-gI/D9VUzI1Jt1G28GYpvm5ckupgJ5O8mi5Y657UyuUozX34ErfVdZ81g6oVcKFQZ60LhCzk7jJeykK48gaLhDw==,
        tarball: https://registry.npmjs.org/@graphql-tools/executor-graphql-ws/-/executor-graphql-ws-2.0.5.tgz,
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/executor-http@1.3.3':
    resolution:
      {
        integrity: sha512-LIy+l08/Ivl8f8sMiHW2ebyck59JzyzO/yF9SFS4NH6MJZUezA1xThUXCDIKhHiD56h/gPojbkpcFvM2CbNE7A==,
        tarball: https://registry.npmjs.org/@graphql-tools/executor-http/-/executor-http-1.3.3.tgz,
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/executor-legacy-ws@1.1.17':
    resolution:
      {
        integrity: sha512-TvltY6eL4DY1Vt66Z8kt9jVmNcI+WkvVPQZrPbMCM3rv2Jw/sWvSwzUBezRuWX0sIckMifYVh23VPcGBUKX/wg==,
        tarball: https://registry.npmjs.org/@graphql-tools/executor-legacy-ws/-/executor-legacy-ws-1.1.17.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/executor@1.4.7':
    resolution:
      {
        integrity: sha512-U0nK9jzJRP9/9Izf1+0Gggd6K6RNRsheFo1gC/VWzfnsr0qjcOSS9qTjY0OTC5iTPt4tQ+W5Zpw/uc7mebI6aA==,
        tarball: https://registry.npmjs.org/@graphql-tools/executor/-/executor-1.4.7.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/git-loader@8.0.24':
    resolution:
      {
        integrity: sha512-ypLC9N2bKNC0QNbrEBTbWKwbV607f7vK2rSGi9uFeGr8E29tWplo6or9V/+TM0ZfIkUsNp/4QX/zKTgo8SbwQg==,
        tarball: https://registry.npmjs.org/@graphql-tools/git-loader/-/git-loader-8.0.24.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/github-loader@8.0.20':
    resolution:
      {
        integrity: sha512-Icch8bKZ1iP3zXCB9I0ded1hda9NPskSSalw7ZM21kXvLiOR5nZhdqPF65gCFkIKo+O4NR4Bp51MkKj+wl+vpg==,
        tarball: https://registry.npmjs.org/@graphql-tools/github-loader/-/github-loader-8.0.20.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/graphql-file-loader@8.0.20':
    resolution:
      {
        integrity: sha512-inds4My+JJxmg5mxKWYtMIJNRxa7MtX+XIYqqD/nu6G4LzQ5KGaBJg6wEl103KxXli7qNOWeVAUmEjZeYhwNEg==,
        tarball: https://registry.npmjs.org/@graphql-tools/graphql-file-loader/-/graphql-file-loader-8.0.20.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/graphql-tag-pluck@8.3.19':
    resolution:
      {
        integrity: sha512-LEw/6IYOUz48HjbWntZXDCzSXsOIM1AyWZrlLoJOrA8QAlhFd8h5Tny7opCypj8FO9VvpPFugWoNDh5InPOEQA==,
        tarball: https://registry.npmjs.org/@graphql-tools/graphql-tag-pluck/-/graphql-tag-pluck-8.3.19.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/import@7.0.19':
    resolution:
      {
        integrity: sha512-Xtku8G4bxnrr6I3hVf8RrBFGYIbQ1OYVjl7jgcy092aBkNZvy1T6EDmXmYXn5F+oLd9Bks3K3WaMm8gma/nM/Q==,
        tarball: https://registry.npmjs.org/@graphql-tools/import/-/import-7.0.19.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/json-file-loader@8.0.18':
    resolution:
      {
        integrity: sha512-JjjIxxewgk8HeMR3npR3YbOkB7fxmdgmqB9kZLWdkRKBxrRXVzhryyq+mhmI0Evzt6pNoHIc3vqwmSctG2sddg==,
        tarball: https://registry.npmjs.org/@graphql-tools/json-file-loader/-/json-file-loader-8.0.18.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/load@8.1.0':
    resolution:
      {
        integrity: sha512-OGfOm09VyXdNGJS/rLqZ6ztCiG2g6AMxhwtET8GZXTbnjptFc17GtKwJ3Jv5w7mjJ8dn0BHydvIuEKEUK4ciYw==,
        tarball: https://registry.npmjs.org/@graphql-tools/load/-/load-8.1.0.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/merge@9.0.24':
    resolution:
      {
        integrity: sha512-NzWx/Afl/1qHT3Nm1bghGG2l4jub28AdvtG11PoUlmjcIjnFBJMv4vqL0qnxWe8A82peWo4/TkVdjJRLXwgGEw==,
        tarball: https://registry.npmjs.org/@graphql-tools/merge/-/merge-9.0.24.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/optimize@1.4.0':
    resolution:
      {
        integrity: sha512-dJs/2XvZp+wgHH8T5J2TqptT9/6uVzIYvA6uFACha+ufvdMBedkfR4b4GbT8jAKLRARiqRTxy3dctnwkTM2tdw==,
        tarball: https://registry.npmjs.org/@graphql-tools/optimize/-/optimize-1.4.0.tgz,
      }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/optimize@2.0.0':
    resolution:
      {
        integrity: sha512-nhdT+CRGDZ+bk68ic+Jw1OZ99YCDIKYA5AlVAnBHJvMawSx9YQqQAIj4refNc1/LRieGiuWvhbG3jvPVYho0Dg==,
        tarball: https://registry.npmjs.org/@graphql-tools/optimize/-/optimize-2.0.0.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/prisma-loader@8.0.17':
    resolution:
      {
        integrity: sha512-fnuTLeQhqRbA156pAyzJYN0KxCjKYRU5bz1q/SKOwElSnAU4k7/G1kyVsWLh7fneY78LoMNH5n+KlFV8iQlnyg==,
        tarball: https://registry.npmjs.org/@graphql-tools/prisma-loader/-/prisma-loader-8.0.17.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/relay-operation-optimizer@6.5.18':
    resolution:
      {
        integrity: sha512-mc5VPyTeV+LwiM+DNvoDQfPqwQYhPV/cl5jOBjTgSniyaq8/86aODfMkrE2OduhQ5E00hqrkuL2Fdrgk0w1QJg==,
        tarball: https://registry.npmjs.org/@graphql-tools/relay-operation-optimizer/-/relay-operation-optimizer-6.5.18.tgz,
      }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/relay-operation-optimizer@7.0.19':
    resolution:
      {
        integrity: sha512-xnjLpfzw63yIX1bo+BVh4j1attSwqEkUbpJ+HAhdiSUa3FOQFfpWgijRju+***************************==,
        tarball: https://registry.npmjs.org/@graphql-tools/relay-operation-optimizer/-/relay-operation-optimizer-7.0.19.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/schema@10.0.23':
    resolution:
      {
        integrity: sha512-aEGVpd1PCuGEwqTXCStpEkmheTHNdMayiIKH1xDWqYp9i8yKv9FRDgkGrY4RD8TNxnf7iII+6KOBGaJ3ygH95A==,
        tarball: https://registry.npmjs.org/@graphql-tools/schema/-/schema-10.0.23.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/url-loader@8.0.31':
    resolution:
      {
        integrity: sha512-QGP3py6DAdKERHO5D38Oi+6j+v0O3rkBbnLpyOo87rmIRbwE6sOkL5JeHegHs7EEJ279fBX6lMt8ry0wBMGtyA==,
        tarball: https://registry.npmjs.org/@graphql-tools/url-loader/-/url-loader-8.0.31.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/utils@10.8.6':
    resolution:
      {
        integrity: sha512-Alc9Vyg0oOsGhRapfL3xvqh1zV8nKoFUdtLhXX7Ki4nClaIJXckrA86j+uxEuG3ic6j4jlM1nvcWXRn/71AVLQ==,
        tarball: https://registry.npmjs.org/@graphql-tools/utils/-/utils-10.8.6.tgz,
      }
    engines: { node: '>=16.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/utils@8.13.1':
    resolution:
      {
        integrity: sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==,
        tarball: https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz,
      }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/utils@9.2.1':
    resolution:
      {
        integrity: sha512-WUw506Ql6xzmOORlriNrD6Ugx+HjVgYxt9KCXD9mHAak+eaXSwuGGPyE60hy9xaDEoXKBsG7SkG69ybitaVl6A==,
        tarball: https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.2.1.tgz,
      }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-tools/wrap@10.1.0':
    resolution:
      {
        integrity: sha512-M7QolM/cJwM2PNAJS1vphT2/PDVSKtmg5m+fxHrFfKpp2RRosJSvYPzUD/PVPqF2rXTtnCwkgh1s5KIsOPCz+w==,
        tarball: https://registry.npmjs.org/@graphql-tools/wrap/-/wrap-10.1.0.tgz,
      }
    engines: { node: '>=18.0.0' }
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@graphql-typed-document-node/core@3.2.0':
    resolution:
      {
        integrity: sha512-mB9oAsNCm9aM3/SOv4YtBMqZbYj10R7dkq8byBqxGY/ncFwhf2oQzMV+LCRlWoDSEBJ3COiR1yeDvMtsoOsuFQ==,
        tarball: https://registry.npmjs.org/@graphql-typed-document-node/core/-/core-3.2.0.tgz,
      }
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@hookform/resolvers@3.10.0':
    resolution:
      {
        integrity: sha512-79Dv+3mDF7i+2ajj7SkypSKHhl1cbln1OGavqrsF7p6mbUv11xpqpacPsGDCTRvCSjEEIez2ef1NveSVL3b0Ag==,
        tarball: https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.10.0.tgz,
      }
    peerDependencies:
      react-hook-form: ^7.0.0

  '@humanfs/core@0.19.1':
    resolution:
      {
        integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==,
        tarball: https://registry.npmjs.org/@humanfs/core/-/core-0.19.1.tgz,
      }
    engines: { node: '>=18.18.0' }

  '@humanfs/node@0.16.6':
    resolution:
      {
        integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==,
        tarball: https://registry.npmjs.org/@humanfs/node/-/node-0.16.6.tgz,
      }
    engines: { node: '>=18.18.0' }

  '@humanwhocodes/module-importer@1.0.1':
    resolution:
      {
        integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==,
        tarball: https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz,
      }
    engines: { node: '>=12.22' }

  '@humanwhocodes/retry@0.3.1':
    resolution:
      {
        integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==,
        tarball: https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.3.1.tgz,
      }
    engines: { node: '>=18.18' }

  '@humanwhocodes/retry@0.4.3':
    resolution:
      {
        integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==,
        tarball: https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.3.tgz,
      }
    engines: { node: '>=18.18' }

  '@isaacs/cliui@8.0.2':
    resolution:
      {
        integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==,
        tarball: https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz,
      }
    engines: { node: '>=12' }

  '@jest/expect-utils@29.7.0':
    resolution:
      {
        integrity: sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==,
        tarball: https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  '@jest/schemas@29.6.3':
    resolution:
      {
        integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==,
        tarball: https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  '@jest/types@29.6.3':
    resolution:
      {
        integrity: sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==,
        tarball: https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  '@jridgewell/gen-mapping@0.3.8':
    resolution:
      {
        integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==,
        tarball: https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz,
      }
    engines: { node: '>=6.0.0' }

  '@jridgewell/resolve-uri@3.1.2':
    resolution:
      {
        integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==,
        tarball: https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz,
      }
    engines: { node: '>=6.0.0' }

  '@jridgewell/set-array@1.2.1':
    resolution:
      {
        integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==,
        tarball: https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz,
      }
    engines: { node: '>=6.0.0' }

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution:
      {
        integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==,
        tarball: https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz,
      }

  '@jridgewell/trace-mapping@0.3.25':
    resolution:
      {
        integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==,
        tarball: https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz,
      }

  '@keyv/serialize@1.0.3':
    resolution:
      {
        integrity: sha512-qnEovoOp5Np2JDGonIDL6Ayihw0RhnRh6vxPuHo4RDn1UOzwEo4AeIfpL6UGIrsceWrCMiVPgwRjbHu4vYFc3g==,
        tarball: https://registry.npmjs.org/@keyv/serialize/-/serialize-1.0.3.tgz,
      }

  '@nodelib/fs.scandir@2.1.5':
    resolution:
      {
        integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==,
        tarball: https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz,
      }
    engines: { node: '>= 8' }

  '@nodelib/fs.stat@2.0.5':
    resolution:
      {
        integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==,
        tarball: https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz,
      }
    engines: { node: '>= 8' }

  '@nodelib/fs.walk@1.2.8':
    resolution:
      {
        integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==,
        tarball: https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz,
      }
    engines: { node: '>= 8' }

  '@peculiar/asn1-schema@2.3.15':
    resolution:
      {
        integrity: sha512-QPeD8UA8axQREpgR5UTAfu2mqQmm97oUqahDtNdBcfj3qAnoXzFdQW+aNf/tD2WVXF8Fhmftxoj0eMIT++gX2w==,
        tarball: https://registry.npmjs.org/@peculiar/asn1-schema/-/asn1-schema-2.3.15.tgz,
      }

  '@peculiar/json-schema@1.1.12':
    resolution:
      {
        integrity: sha512-coUfuoMeIB7B8/NMekxaDzLhaYmp0HZNPEjYRm9goRou8UZIC3z21s0sL9AWoCw4EG876QyO3kYrc61WNF9B/w==,
        tarball: https://registry.npmjs.org/@peculiar/json-schema/-/json-schema-1.1.12.tgz,
      }
    engines: { node: '>=8.0.0' }

  '@peculiar/webcrypto@1.5.0':
    resolution:
      {
        integrity: sha512-BRs5XUAwiyCDQMsVA9IDvDa7UBR9gAvPHgugOeGng3YN6vJ9JYonyDc0lNczErgtCWtucjR5N7VtaonboD/ezg==,
        tarball: https://registry.npmjs.org/@peculiar/webcrypto/-/webcrypto-1.5.0.tgz,
      }
    engines: { node: '>=10.12.0' }

  '@pkgjs/parseargs@0.11.0':
    resolution:
      {
        integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==,
        tarball: https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz,
      }
    engines: { node: '>=14' }

  '@pkgr/core@0.2.7':
    resolution:
      {
        integrity: sha512-YLT9Zo3oNPJoBjBc4q8G2mjU4tqIbf5CEOORbUUr48dCD9q3umJ3IPlVqOqDakPfd2HuwccBaqlGhN4Gmr5OWg==,
        tarball: https://registry.npmjs.org/@pkgr/core/-/core-0.2.7.tgz,
      }
    engines: { node: ^12.20.0 || ^14.18.0 || >=16.0.0 }

  '@popperjs/core@2.11.8':
    resolution:
      {
        integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==,
        tarball: https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz,
      }

  '@radix-ui/number@1.1.0':
    resolution:
      {
        integrity: sha512-V3gRzhVNU1ldS5XhAPTom1fOIo4ccrjjJgmE+LI2h/WaFpHmx0MQApT+KZHnx8abG6Avtfcz4WoEciMnpFT3HQ==,
        tarball: https://registry.npmjs.org/@radix-ui/number/-/number-1.1.0.tgz,
      }

  '@radix-ui/primitive@1.1.1':
    resolution:
      {
        integrity: sha512-SJ31y+Q/zAyShtXJc8x83i9TYdbAfHZ++tUZnvjJJqFjzsdUnKsxPL6IEtBlxKkU7yzer//GQtZSV4GbldL3YA==,
        tarball: https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.1.tgz,
      }

  '@radix-ui/react-accordion@1.2.3':
    resolution:
      {
        integrity: sha512-RIQ15mrcvqIkDARJeERSuXSry2N8uYnxkdDetpfmalT/+0ntOXLkFOsh9iwlAsCv+qcmhZjbdJogIm6WBa6c4A==,
        tarball: https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.3.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-arrow@1.1.1':
    resolution:
      {
        integrity: sha512-NaVpZfmv8SKeZbn4ijN2V3jlHA9ngBG16VnIIm22nUR0Yk8KUALyBxT3KYEUnNuch9sTE8UTsS3whzBgKOL30w==,
        tarball: https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-arrow@1.1.2':
    resolution:
      {
        integrity: sha512-G+KcpzXHq24iH0uGG/pF8LyzpFJYGD4RfLjCIBfGdSLXvjLHST31RUiRVrupIBMvIppMgSzQ6l66iAxl03tdlg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-checkbox@1.1.4':
    resolution:
      {
        integrity: sha512-wP0CPAHq+P5I4INKe3hJrIa1WoNqqrejzW+zoU0rOvo1b9gDEJJFl2rYfO1PYJUQCc2H1WZxIJmyv9BS8i5fLw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.4.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collapsible@1.1.3':
    resolution:
      {
        integrity: sha512-jFSerheto1X03MUC0g6R7LedNW9EEGWdg9W1+MlpkMLwGkgkbUXLPBH/KIuWKXUoeYRVY11llqbTBDzuLg7qrw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.3.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.1':
    resolution:
      {
        integrity: sha512-LwT3pSho9Dljg+wY2KN2mrrh6y3qELfftINERIzBUO9e0N+t0oMTyn3k9iv+ZqgrwGkRnLpNJrsMv9BZlt2yuA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.2':
    resolution:
      {
        integrity: sha512-9z54IEKRxIa9VityapoEYMuByaG42iSy1ZXlY2KcuLSEtq8x4987/N6m15ppoMffgZX72gER2uHe1D9Y6Unlcw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-compose-refs@1.1.1':
    resolution:
      {
        integrity: sha512-Y9VzoRDSJtgFMUCoiZBDVo084VQ5hfpXxVE+NgkdNsjiDBByiImMZKKhxMwCbdHvhlENG6a833CbFkOQvTricw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-context@1.1.1':
    resolution:
      {
        integrity: sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==,
        tarball: https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dialog@1.1.4':
    resolution:
      {
        integrity: sha512-Ur7EV1IwQGCyaAuyDRiOLA5JIUZxELJljF+MbM/2NC0BYwfuRrbpS30BiQBJrVruscgUkieKkqXYDOoByaxIoA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.4.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-direction@1.1.0':
    resolution:
      {
        integrity: sha512-BUuBvgThEiAXh2DWu93XsT+a3aWrGqolGlqqw5VU1kG7p/ZH2cuDlM1sRLNnY3QcBS69UIz2mcKhMxDsdewhjg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.0.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dismissable-layer@1.1.3':
    resolution:
      {
        integrity: sha512-onrWn/72lQoEucDmJnr8uczSNTujT0vJnA/X5+3AkChVPowr8n1yvIKIabhWyMQeMvvmdpsvcyDqx3X1LEXCPg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.3.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-dismissable-layer@1.1.5':
    resolution:
      {
        integrity: sha512-E4TywXY6UsXNRhFrECa5HAvE5/4BFcGyfTyK36gP+pAW1ed7UTK4vKwdr53gAJYwqbfCWC6ATvJa3J3R/9+Qrg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.5.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-dropdown-menu@2.1.6':
    resolution:
      {
        integrity: sha512-no3X7V5fD487wab/ZYSHXq3H37u4NVeLDKI/Ks724X/eEFSSEFYZxWgsIlr1UBeEyDaM29HM5x9p1Nv8DuTYPA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-dropdown-menu/-/react-dropdown-menu-2.1.6.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-guards@1.1.1':
    resolution:
      {
        integrity: sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-focus-scope@1.1.1':
    resolution:
      {
        integrity: sha512-01omzJAYRxXdG2/he/+xy+c8a8gCydoQ1yOxnWNcRhrrBW5W+RQJ22EK1SaO8tb3WoUsuEw7mJjBozPzihDFjA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-scope@1.1.2':
    resolution:
      {
        integrity: sha512-zxwE80FCU7lcXUGWkdt6XpTTCKPitG1XKOwViTxHVKIJhZl9MvIl2dVHeZENCWD9+EdWv05wlaEkRXUykU27RA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-id@1.1.0':
    resolution:
      {
        integrity: sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.0.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-label@2.1.1':
    resolution:
      {
        integrity: sha512-UUw5E4e/2+4kFMH7+YxORXGWggtY6sM8WIwh5RZchhLuUg2H1hc98Py+pr8HMz6rdaYrK2t296ZEjYLOCO5uUw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-menu@2.1.6':
    resolution:
      {
        integrity: sha512-tBBb5CXDJW3t2mo9WlO7r6GTmWV0F0uzHZVFmlRmYpiSK1CDU5IKojP1pm7oknpBOrFZx/YgBRW9oorPO2S/Lg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-menu/-/react-menu-2.1.6.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popover@1.1.4':
    resolution:
      {
        integrity: sha512-aUACAkXx8LaFymDma+HQVji7WhvEhpFJ7+qPz17Nf4lLZqtreGOFRiNQWQmhzp7kEWg9cOyyQJpdIMUMPc/CPw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-popover/-/react-popover-1.1.4.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.2.1':
    resolution:
      {
        integrity: sha512-3kn5Me69L+jv82EKRuQCXdYyf1DqHwD2U/sxoNgBGCB7K9TRc3bQamQ+5EPM9EvyPdli0W41sROd+ZU1dTCztw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.2.2':
    resolution:
      {
        integrity: sha512-Rvqc3nOpwseCyj/rgjlJDYAgyfw7OC1tTkKn2ivhaMGcYt8FSBlahHOZak2i3QwkRXUXgGgzeEe2RuqeEHuHgA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.1.3':
    resolution:
      {
        integrity: sha512-NciRqhXnGojhT93RPyDaMPfLH3ZSl4jjIFbZQ1b/vxvZEdHsBZ49wP9w8L3HzUQwep01LcWtkUvm0OVB5JAHTw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.3.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.1.4':
    resolution:
      {
        integrity: sha512-sn2O9k1rPFYVyKd5LAJfo96JlSGVFpa1fS6UuBJfrZadudiw5tAmru+n1x7aMRQ84qDM71Zh1+SzK5QwU0tJfA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.4.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-presence@1.1.2':
    resolution:
      {
        integrity: sha512-18TFr80t5EVgL9x1SwF/YGtfG+l0BS0PRAlCWBDoBEiDQjeKgnNZRVJp/oVBl24sr3Gbfwc/Qpj4OcWTQMsAEg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.0.1':
    resolution:
      {
        integrity: sha512-sHCWTtxwNn3L3fH8qAfnF3WbUZycW93SM1j3NFDzXBiz8D6F5UTTy8G1+WFEaiCdvCVRJWj6N2R4Xq6HdiHmDg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.0.2':
    resolution:
      {
        integrity: sha512-Ec/0d38EIuvDF+GZjcMU/Ze6MxntVJYO/fRlCPhCaVUyPY9WTalHJw54tp9sXeJo3tlShWpy41vQRgLRGOuz+w==,
        tarball: https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-radio-group@1.2.3':
    resolution:
      {
        integrity: sha512-xtCsqt8Rp09FK50ItqEqTJ7Sxanz8EM8dnkVIhJrc/wkMMomSmXHvYbhv3E7Zx4oXh98aaLt9W679SUYXg4IDA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-radio-group/-/react-radio-group-1.2.3.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-roving-focus@1.1.2':
    resolution:
      {
        integrity: sha512-zgMQWkNO169GtGqRvYrzb0Zf8NhMHS2DuEB/TiEmVnpr5OqPU3i8lfbxaAmC2J/KYuIQxyoQQ6DxepyXp61/xw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-select@2.1.6':
    resolution:
      {
        integrity: sha512-T6ajELxRvTuAMWH0YmRJ1qez+x4/7Nq7QIx7zJ0VK3qaEWdnWpNbEDnmWldG1zBDwqrLy5aLMUWcoGirVj5kMg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-select/-/react-select-2.1.6.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-separator@1.1.2':
    resolution:
      {
        integrity: sha512-oZfHcaAp2Y6KFBX6I5P1u7CQoy4lheCGiYj+pGFrHy8E/VNRb5E39TkTr3JrV520csPBTZjkuKFdEsjS5EUNKQ==,
        tarball: https://registry.npmjs.org/@radix-ui/react-separator/-/react-separator-1.1.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slider@1.2.2':
    resolution:
      {
        integrity: sha512-sNlU06ii1/ZcbHf8I9En54ZPW0Vil/yPVg4vQMcFNjrIx51jsHbFl1HYHQvCIWJSr1q0ZmA+iIs/ZTv8h7HHSA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slot@1.1.1':
    resolution:
      {
        integrity: sha512-RApLLOcINYJA+dMVbOju7MYv1Mb2EBp2nH4HdDzXTSyaR5optlm6Otrz1euW3HbdOR8UmmFK06TD+A9frYWv+g==,
        tarball: https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-slot@1.1.2':
    resolution:
      {
        integrity: sha512-YAKxaiGsSQJ38VzKH86/BPRC4rh+b1Jpa+JneA5LRE7skmLPNAyeG8kPJj/oo4STLvlrs8vkf/iYyc3A5stYCQ==,
        tarball: https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-switch@1.1.2':
    resolution:
      {
        integrity: sha512-zGukiWHjEdBCRyXvKR6iXAQG6qXm2esuAD6kDOi9Cn+1X6ev3ASo4+CsYaD6Fov9r/AQFekqnD/7+V0Cs6/98g==,
        tarball: https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-tooltip@1.1.6':
    resolution:
      {
        integrity: sha512-TLB5D8QLExS1uDn7+wH/bjEmRurNMTzNrtq7IjaS4kjion9NtzsTGkvR5+i7yc9q01Pi2KMM2cN3f8UG4IvvXA==,
        tarball: https://registry.npmjs.org/@radix-ui/react-tooltip/-/react-tooltip-1.1.6.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-use-callback-ref@1.1.0':
    resolution:
      {
        integrity: sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-controllable-state@1.1.0':
    resolution:
      {
        integrity: sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.0.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-escape-keydown@1.1.0':
    resolution:
      {
        integrity: sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-layout-effect@1.1.0':
    resolution:
      {
        integrity: sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==,
        tarball: https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.0.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-previous@1.1.0':
    resolution:
      {
        integrity: sha512-Z/e78qg2YFnnXcW88A4JmTtm4ADckLno6F7OXotmkQfeuCVaKuYzqAATPhVzl3delXE7CxIV8shofPn3jPc5Og==,
        tarball: https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-rect@1.1.0':
    resolution:
      {
        integrity: sha512-0Fmkebhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==,
        tarball: https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-size@1.1.0':
    resolution:
      {
        integrity: sha512-XW3/vWuIXHa+2Uwcc2ABSfcCledmXhhQPlGbfcRXbiUQI5Icjcg19BGCZVKKInYbvUCut/ufbbLLPFC5cbb1hw==,
        tarball: https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.0.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-visually-hidden@1.1.1':
    resolution:
      {
        integrity: sha512-vVfA2IZ9q/J+gEamvj761Oq1FpWgCDaNOOIfbPVp2MVPLEomUr5+Vf7kJGwQ24YxZSlQVar7Bes8kyTo5Dshpg==,
        tarball: https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.1.1.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-visually-hidden@1.1.2':
    resolution:
      {
        integrity: sha512-1SzA4ns2M1aRlvxErqhLHsBHoS5eI5UUcI2awAMgGUp4LoaoWOKYmvqDY2s/tltuPkh3Yk77YF/r3IRj+Amx4Q==,
        tarball: https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.1.2.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/rect@1.1.0':
    resolution:
      {
        integrity: sha512-A9+lCBZoaMJlVKcRBz2YByCG+Cp2t6nAnMnNba+XiWxnj6r4JUFqfsgwocMBZU9LPtdxC6wB56ySYpc7LQIoJg==,
        tarball: https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.0.tgz,
      }

  '@react-spring/types@9.7.3':
    resolution:
      {
        integrity: sha512-Kpx/fQ/ZFX31OtlqVEFfgaD1ACzul4NksrvIgYfIFq9JpDHFwQkMVZ10tbo0FU/grje4rcL4EIrjekl3kYwgWw==,
        tarball: https://registry.npmjs.org/@react-spring/types/-/types-9.7.3.tgz,
      }

  '@remix-run/router@1.15.3':
    resolution:
      {
        integrity: sha512-Oy8rmScVrVxWZVOpEF57ovlnhpZ8CCPlnIIumVcV9nFdiSIrus99+Lw78ekXyGvVDlIsFJbSfmSovJUhCWYV3w==,
        tarball: https://registry.npmjs.org/@remix-run/router/-/router-1.15.3.tgz,
      }
    engines: { node: '>=14.0.0' }

  '@repeaterjs/repeater@3.0.6':
    resolution:
      {
        integrity: sha512-Javneu5lsuhwNCryN+pXH93VPQ8g0dBX7wItHFgYiwQmzE1sVdg5tWHiOgHywzL2W21XQopa7IwIEnNbmeUJYA==,
        tarball: https://registry.npmjs.org/@repeaterjs/repeater/-/repeater-3.0.6.tgz,
      }

  '@rollup/pluginutils@5.1.4':
    resolution:
      {
        integrity: sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ==,
        tarball: https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.1.4.tgz,
      }
    engines: { node: '>=14.0.0' }
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.41.1':
    resolution:
      {
        integrity: sha512-NELNvyEWZ6R9QMkiytB4/L4zSEaBC03KIXEghptLGLZWJ6VPrL63ooZQCOnlx36aQPGhzuOMwDerC1Eb2VmrLw==,
        tarball: https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.41.1.tgz,
      }
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.41.1':
    resolution:
      {
        integrity: sha512-DXdQe1BJ6TK47ukAoZLehRHhfKnKg9BjnQYUu9gzhI8Mwa1d2fzxA1aw2JixHVl403bwp1+/o/NhhHtxWJBgEA==,
        tarball: https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.1.tgz,
      }
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.41.1':
    resolution:
      {
        integrity: sha512-5afxvwszzdulsU2w8JKWwY8/sJOLPzf0e1bFuvcW5h9zsEg+RQAojdW0ux2zyYAz7R8HvvzKCjLNJhVq965U7w==,
        tarball: https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.41.1.tgz,
      }
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.41.1':
    resolution:
      {
        integrity: sha512-egpJACny8QOdHNNMZKf8xY0Is6gIMz+tuqXlusxquWu3F833DcMwmGM7WlvCO9sB3OsPjdC4U0wHw5FabzCGZg==,
        tarball: https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.41.1.tgz,
      }
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.41.1':
    resolution:
      {
        integrity: sha512-DBVMZH5vbjgRk3r0OzgjS38z+atlupJ7xfKIDJdZZL6sM6wjfDNo64aowcLPKIx7LMQi8vybB56uh1Ftck/Atg==,
        tarball: https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.41.1.tgz,
      }
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.41.1':
    resolution:
      {
        integrity: sha512-3FkydeohozEskBxNWEIbPfOE0aqQgB6ttTkJ159uWOFn42VLyfAiyD9UK5mhu+ItWzft60DycIN1Xdgiy8o/SA==,
        tarball: https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.41.1.tgz,
      }
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    resolution:
      {
        integrity: sha512-wC53ZNDgt0pqx5xCAgNunkTzFE8GTgdZ9EwYGVcg+jEjJdZGtq9xPjDnFgfFozQI/Xm1mh+D9YlYtl+ueswNEg==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.41.1.tgz,
      }
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    resolution:
      {
        integrity: sha512-jwKCca1gbZkZLhLRtsrka5N8sFAaxrGz/7wRJ8Wwvq3jug7toO21vWlViihG85ei7uJTpzbXZRcORotE+xyrLA==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.41.1.tgz,
      }
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    resolution:
      {
        integrity: sha512-g0UBcNknsmmNQ8V2d/zD2P7WWfJKU0F1nu0k5pW4rvdb+BIqMm8ToluW/eeRmxCared5dD76lS04uL4UaNgpNA==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.41.1.tgz,
      }
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-arm64-musl@4.41.1':
    resolution:
      {
        integrity: sha512-XZpeGB5TKEZWzIrj7sXr+BEaSgo/ma/kCgrZgL0oo5qdB1JlTzIYQKel/RmhT6vMAvOdM2teYlAaOGJpJ9lahg==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.41.1.tgz,
      }
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    resolution:
      {
        integrity: sha512-bkCfDJ4qzWfFRCNt5RVV4DOw6KEgFTUZi2r2RuYhGWC8WhCA8lCAJhDeAmrM/fdiAH54m0mA0Vk2FGRPyzI+tw==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.41.1.tgz,
      }
    cpu: [loong64]
    os: [linux]

  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    resolution:
      {
        integrity: sha512-3mr3Xm+gvMX+/8EKogIZSIEF0WUu0HL9di+YWlJpO8CQBnoLAEL/roTCxuLncEdgcfJcvA4UMOf+2dnjl4Ut1A==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.41.1.tgz,
      }
    cpu: [ppc64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    resolution:
      {
        integrity: sha512-3rwCIh6MQ1LGrvKJitQjZFuQnT2wxfU+ivhNBzmxXTXPllewOF7JR1s2vMX/tWtUYFgphygxjqMl76q4aMotGw==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.41.1.tgz,
      }
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    resolution:
      {
        integrity: sha512-LdIUOb3gvfmpkgFZuccNa2uYiqtgZAz3PTzjuM5bH3nvuy9ty6RGc/Q0+HDFrHrizJGVpjnTZ1yS5TNNjFlklw==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.41.1.tgz,
      }
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    resolution:
      {
        integrity: sha512-oIE6M8WC9ma6xYqjvPhzZYk6NbobIURvP/lEbh7FWplcMO6gn7MM2yHKA1eC/GvYwzNKK/1LYgqzdkZ8YFxR8g==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.41.1.tgz,
      }
    cpu: [s390x]
    os: [linux]

  '@rollup/rollup-linux-x64-gnu@4.41.1':
    resolution:
      {
        integrity: sha512-cWBOvayNvA+SyeQMp79BHPK8ws6sHSsYnK5zDcsC3Hsxr1dgTABKjMnMslPq1DvZIp6uO7kIWhiGwaTdR4Og9A==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.41.1.tgz,
      }
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-linux-x64-musl@4.41.1':
    resolution:
      {
        integrity: sha512-y5CbN44M+pUCdGDlZFzGGBSKCA4A/J2ZH4edTYSSxFg7ce1Xt3GtydbVKWLlzL+INfFIZAEg1ZV6hh9+QQf9YQ==,
        tarball: https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.41.1.tgz,
      }
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    resolution:
      {
        integrity: sha512-lZkCxIrjlJlMt1dLO/FbpZbzt6J/A8p4DnqzSa4PWqPEUUUnzXLeki/iyPLfV0BmHItlYgHUqJe+3KiyydmiNQ==,
        tarball: https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.1.tgz,
      }
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    resolution:
      {
        integrity: sha512-+psFT9+pIh2iuGsxFYYa/LhS5MFKmuivRsx9iPJWNSGbh2XVEjk90fmpUEjCnILPEPJnikAU6SFDiEUyOv90Pg==,
        tarball: https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.41.1.tgz,
      }
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.41.1':
    resolution:
      {
        integrity: sha512-Wq2zpapRYLfi4aKxf2Xff0tN+7slj2d4R87WEzqw7ZLsVvO5zwYCIuEGSZYiK41+GlwUo1HiR+GdkLEJnCKTCw==,
        tarball: https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.41.1.tgz,
      }
    cpu: [x64]
    os: [win32]

  '@rudderstack/analytics-js@3.7.11':
    resolution:
      {
        integrity: sha512-l4s+iGwfNatM4ZmtYpzS/ZAVYG875878ymIxBuWLdC32VG1LZhosNz3dWbmYVU6PehwdsZO6nQbNckeU6ymntw==,
        tarball: https://registry.npmjs.org/@rudderstack/analytics-js/-/analytics-js-3.7.11.tgz,
      }

  '@savvywombat/tailwindcss-grid-areas@4.0.0':
    resolution:
      {
        integrity: sha512-gjEGTltsGfy+AJQqhkuQKQGp/SB0XzXT3ZNmrOfuwPGF5DLg5GsN5XHiaYhudMvmBwRp+9k6wrZhHBM0lhEgJA==,
        tarball: https://registry.npmjs.org/@savvywombat/tailwindcss-grid-areas/-/tailwindcss-grid-areas-4.0.0.tgz,
      }
    engines: { node: '>=16.20.2' }
    peerDependencies:
      tailwindcss: ^3.0.1

  '@sentry-internal/feedback@7.108.0':
    resolution:
      {
        integrity: sha512-8JcgZEnk1uWrXJhsd3iRvFtEiVeaWOEhN0NZwhwQXHfvODqep6JtrkY1yCIyxbpA37aZmrPc2JhyotRERGfUjg==,
        tarball: https://registry.npmjs.org/@sentry-internal/feedback/-/feedback-7.108.0.tgz,
      }
    engines: { node: '>=12' }

  '@sentry-internal/replay-canvas@7.108.0':
    resolution:
      {
        integrity: sha512-R5tvjGqWUV5vSk0N1eBgVW7wIADinrkfDEBZ9FyKP2mXHBobsyNGt30heJDEqYmVqluRqjU2NuIRapsnnrpGnA==,
        tarball: https://registry.npmjs.org/@sentry-internal/replay-canvas/-/replay-canvas-7.108.0.tgz,
      }
    engines: { node: '>=12' }

  '@sentry-internal/tracing@7.108.0':
    resolution:
      {
        integrity: sha512-zuK5XsTsb+U+hgn3SPetYDAogrXsM16U/LLoMW7+TlC6UjlHGYQvmX3o+M2vntejoU1QZS8m1bCAZSMWEypAEw==,
        tarball: https://registry.npmjs.org/@sentry-internal/tracing/-/tracing-7.108.0.tgz,
      }
    engines: { node: '>=8' }

  '@sentry/babel-plugin-component-annotate@2.16.1':
    resolution:
      {
        integrity: sha512-pJka66URsqQbk6hTs9H1XFpUeI0xxuqLYf9Dy5pRGNHSJMtfv91U+CaYSWt03aRRMGDXMduh62zAAY7Wf0HO+A==,
        tarball: https://registry.npmjs.org/@sentry/babel-plugin-component-annotate/-/babel-plugin-component-annotate-2.16.1.tgz,
      }
    engines: { node: '>= 14' }

  '@sentry/browser@7.108.0':
    resolution:
      {
        integrity: sha512-FNpzsdTvGvdHJMUelqEouUXMZU7jC+dpN7CdT6IoHVVFEkoAgrjMVUhXZoQ/dmCkdKWHmFSQhJ8Fm6V+e9Aq0A==,
        tarball: https://registry.npmjs.org/@sentry/browser/-/browser-7.108.0.tgz,
      }
    engines: { node: '>=8' }

  '@sentry/bundler-plugin-core@2.16.1':
    resolution:
      {
        integrity: sha512-n6z8Ts3T9HROLuY7tVEYpBKvS+P7+b8NdqxP7QBcwp2nuPUlN5Ola1ivFjk1p5a7wRYeN9zM8orGe4l2HeNfYA==,
        tarball: https://registry.npmjs.org/@sentry/bundler-plugin-core/-/bundler-plugin-core-2.16.1.tgz,
      }
    engines: { node: '>= 14' }

  '@sentry/cli-darwin@2.30.2':
    resolution:
      {
        integrity: sha512-lZkKXMt0HUAwLQuPpi/DM3CsdCCp+6B2cdur+8fAq7uARXTOsTKVDxv9pkuJHCgHUnguh8ittP5GMr0baTxmMg==,
        tarball: https://registry.npmjs.org/@sentry/cli-darwin/-/cli-darwin-2.30.2.tgz,
      }
    engines: { node: '>=10' }
    os: [darwin]

  '@sentry/cli-linux-arm64@2.30.2':
    resolution:
      {
        integrity: sha512-IWassuXggNhHOPCNrORNmd5SrAx5rU4XDlgOWBJr/ez7DvlPrr9EhV1xsdht6K4mPXhCGJq3rtRdCoWGJQW6Uw==,
        tarball: https://registry.npmjs.org/@sentry/cli-linux-arm64/-/cli-linux-arm64-2.30.2.tgz,
      }
    engines: { node: '>=10' }
    cpu: [arm64]
    os: [linux, freebsd]

  '@sentry/cli-linux-arm@2.30.2':
    resolution:
      {
        integrity: sha512-H7hqiLpEL7w/EHdhuUGatwg9O080mdujq4/zS96buKIHXxZE6KqMXGtMVIAvTl1+z6BlBEnfvZGI19MPw3t/7w==,
        tarball: https://registry.npmjs.org/@sentry/cli-linux-arm/-/cli-linux-arm-2.30.2.tgz,
      }
    engines: { node: '>=10' }
    cpu: [arm]
    os: [linux, freebsd]

  '@sentry/cli-linux-i686@2.30.2':
    resolution:
      {
        integrity: sha512-gZIq131M4TJTG1lX9uvpoaGWaEXCEfdDXrXu/z/YZmAKBcThpMYChodXmm8FB6X4xb0TPXzIFqdzlLdglFK46g==,
        tarball: https://registry.npmjs.org/@sentry/cli-linux-i686/-/cli-linux-i686-2.30.2.tgz,
      }
    engines: { node: '>=10' }
    cpu: [x86, ia32]
    os: [linux, freebsd]

  '@sentry/cli-linux-x64@2.30.2':
    resolution:
      {
        integrity: sha512-NmTAIl7aW9OHxwB4149sBfvCbTyK9T/CvBX38keaD2yIThet9gZ4koP49hBDxYF99aQX3E+LIAqWwnkV9W72Sw==,
        tarball: https://registry.npmjs.org/@sentry/cli-linux-x64/-/cli-linux-x64-2.30.2.tgz,
      }
    engines: { node: '>=10' }
    cpu: [x64]
    os: [linux, freebsd]

  '@sentry/cli-win32-i686@2.30.2':
    resolution:
      {
        integrity: sha512-SBR/Q3T6o+7uHwHNdjcG9GA3R++9w8oi778b95GuOC3dh0WOU6hXaKwQWe95ZcuSd2rKpouH7dhMjqqNM4HxOA==,
        tarball: https://registry.npmjs.org/@sentry/cli-win32-i686/-/cli-win32-i686-2.30.2.tgz,
      }
    engines: { node: '>=10' }
    cpu: [x86, ia32]
    os: [win32]

  '@sentry/cli-win32-x64@2.30.2':
    resolution:
      {
        integrity: sha512-gF9wSZxzXFgakkC+uKVLAAYlbYj13e1gTsNm3gm+ODfpV+rbHwvbKoLfNsbVCFVCEZxIV2rXEP5WmTr0kiMvWQ==,
        tarball: https://registry.npmjs.org/@sentry/cli-win32-x64/-/cli-win32-x64-2.30.2.tgz,
      }
    engines: { node: '>=10' }
    cpu: [x64]
    os: [win32]

  '@sentry/cli@2.30.2':
    resolution:
      {
        integrity: sha512-jQ/RBJ3bZ4PFbfOsGq8EykygHHmXXPw+i6jqsnQfAPIeZoX+DsqpAZbYubQEZKekmQ8EVGFxGHzUVkd6hLVMbA==,
        tarball: https://registry.npmjs.org/@sentry/cli/-/cli-2.30.2.tgz,
      }
    engines: { node: '>= 10' }
    hasBin: true

  '@sentry/core@7.108.0':
    resolution:
      {
        integrity: sha512-I/VNZCFgLASxHZaD0EtxZRM34WG9w2gozqgrKGNMzAymwmQ3K9g/1qmBy4e6iS3YRptb7J5UhQkZQHrcwBbjWQ==,
        tarball: https://registry.npmjs.org/@sentry/core/-/core-7.108.0.tgz,
      }
    engines: { node: '>=8' }

  '@sentry/react@7.108.0':
    resolution:
      {
        integrity: sha512-C60arh5/gtO42eMU9l34aWlKDLZUO+1j1goaEf/XRSwUcyJS9tbJrs+mT4nbKxUsEG714It2gRbfSEvh1eXmCg==,
        tarball: https://registry.npmjs.org/@sentry/react/-/react-7.108.0.tgz,
      }
    engines: { node: '>=8' }
    peerDependencies:
      react: 15.x || 16.x || 17.x || 18.x

  '@sentry/replay@7.108.0':
    resolution:
      {
        integrity: sha512-jo8fDOzcZJclP1+4n9jUtVxTlBFT9hXwxhAMrhrt70FV/nfmCtYQMD3bzIj79nwbhUtFP6pN39JH1o7Xqt1hxQ==,
        tarball: https://registry.npmjs.org/@sentry/replay/-/replay-7.108.0.tgz,
      }
    engines: { node: '>=12' }

  '@sentry/types@7.108.0':
    resolution:
      {
        integrity: sha512-bKtHITmBN3kqtqE5eVvL8mY8znM05vEodENwRpcm6TSrrBjC2RnwNWVwGstYDdHpNfFuKwC8mLY9bgMJcENo8g==,
        tarball: https://registry.npmjs.org/@sentry/types/-/types-7.108.0.tgz,
      }
    engines: { node: '>=8' }

  '@sentry/utils@7.108.0':
    resolution:
      {
        integrity: sha512-a45yEFD5qtgZaIFRAcFkG8C8lnDzn6t4LfLXuV4OafGAy/3ZAN3XN8wDnrruHkiUezSSANGsLg3bXaLW/JLvJw==,
        tarball: https://registry.npmjs.org/@sentry/utils/-/utils-7.108.0.tgz,
      }
    engines: { node: '>=8' }

  '@sentry/vite-plugin@2.16.1':
    resolution:
      {
        integrity: sha512-RSIyeqFG3PR5iJsZnagQxzOhM22z1Kh9DG+HQQsfVrxokzrWKRu/G17O2MIDh2I5iYEaL0Fkd/9RAXE4/b0aVg==,
        tarball: https://registry.npmjs.org/@sentry/vite-plugin/-/vite-plugin-2.16.1.tgz,
      }
    engines: { node: '>= 14' }

  '@sinclair/typebox@0.27.8':
    resolution:
      {
        integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==,
        tarball: https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz,
      }

  '@svgr/babel-plugin-add-jsx-attribute@8.0.0':
    resolution:
      {
        integrity: sha512-b9MIk7yhdS1pMCZM8VeNfUlSKVRhsHZNMl5O9SfaX0l0t5wjdgu4IDzGB8bpnGBBOjGST3rRFVsaaEtI4W6f7g==,
        tarball: https://registry.npmjs.org/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-8.0.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-remove-jsx-attribute@8.0.0':
    resolution:
      {
        integrity: sha512-BcCkm/STipKvbCl6b7QFrMh/vx00vIP63k2eM66MfHJzPr6O2U0jYEViXkHJWqXqQYjdeA9cuCl5KWmlwjDvbA==,
        tarball: https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-8.0.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0':
    resolution:
      {
        integrity: sha512-5BcGCBfBxB5+XSDSWnhTThfI9jcO5f0Ai2V24gZpG+wXF14BzwxxdDb4g6trdOux0rhibGs385BeFMSmxtS3uA==,
        tarball: https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-8.0.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0':
    resolution:
      {
        integrity: sha512-KVQ+PtIjb1BuYT3ht8M5KbzWBhdAjjUPdlMtpuw/VjT8coTrItWX6Qafl9+ji831JaJcu6PJNKCV0bp01lBNzQ==,
        tarball: https://registry.npmjs.org/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-8.0.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-svg-dynamic-title@8.0.0':
    resolution:
      {
        integrity: sha512-omNiKqwjNmOQJ2v6ge4SErBbkooV2aAWwaPFs2vUY7p7GhVkzRkJ00kILXQvRhA6miHnNpXv7MRnnSjdRjK8og==,
        tarball: https://registry.npmjs.org/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-8.0.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-svg-em-dimensions@8.0.0':
    resolution:
      {
        integrity: sha512-mURHYnu6Iw3UBTbhGwE/vsngtCIbHE43xCRK7kCw4t01xyGqb2Pd+WXekRRoFOBIY29ZoOhUCTEweDMdrjfi9g==,
        tarball: https://registry.npmjs.org/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-8.0.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-transform-react-native-svg@8.1.0':
    resolution:
      {
        integrity: sha512-Tx8T58CHo+7nwJ+EhUwx3LfdNSG9R2OKfaIXXs5soiy5HtgoAEkDay9LIimLOcG8dJQH1wPZp/cnAv6S9CrR1Q==,
        tarball: https://registry.npmjs.org/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-8.1.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-transform-svg-component@8.0.0':
    resolution:
      {
        integrity: sha512-DFx8xa3cZXTdb/k3kfPeaixecQLgKh5NVBMwD0AQxOzcZawK4oo1Jh9LbrcACUivsCA7TLG8eeWgrDXjTMhRmw==,
        tarball: https://registry.npmjs.org/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-8.0.0.tgz,
      }
    engines: { node: '>=12' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-preset@8.1.0':
    resolution:
      {
        integrity: sha512-7EYDbHE7MxHpv4sxvnVPngw5fuR6pw79SkcrILHJ/iMpuKySNCl5W1qcwPEpU+LgyRXOaAFgH0KhwD18wwg6ug==,
        tarball: https://registry.npmjs.org/@svgr/babel-preset/-/babel-preset-8.1.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/core@8.1.0':
    resolution:
      {
        integrity: sha512-8QqtOQT5ACVlmsvKOJNEaWmRPmcojMOzCz4Hs2BGG/toAp/K38LcsMRyLp349glq5AzJbCEeimEoxaX6v/fLrA==,
        tarball: https://registry.npmjs.org/@svgr/core/-/core-8.1.0.tgz,
      }
    engines: { node: '>=14' }

  '@svgr/hast-util-to-babel-ast@8.0.0':
    resolution:
      {
        integrity: sha512-EbDKwO9GpfWP4jN9sGdYwPBU0kdomaPIL2Eu4YwmgP+sJeXT+L7bMwJUBnhzfH8Q2qMBqZ4fJwpCyYsAN3mt2Q==,
        tarball: https://registry.npmjs.org/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-8.0.0.tgz,
      }
    engines: { node: '>=14' }

  '@svgr/plugin-jsx@8.1.0':
    resolution:
      {
        integrity: sha512-0xiIyBsLlr8quN+WyuxooNW9RJ0Dpr8uOnH/xrCVO8GLUcwHISwj1AG0k+LFzteTkAA0GbX0kj9q6Dk70PTiPA==,
        tarball: https://registry.npmjs.org/@svgr/plugin-jsx/-/plugin-jsx-8.1.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      '@svgr/core': '*'

  '@swc/core-darwin-arm64@1.11.31':
    resolution:
      {
        integrity: sha512-NTEaYOts0OGSbJZc0O74xsji+64JrF1stmBii6D5EevWEtrY4wlZhm8SiP/qPrOB+HqtAihxWIukWkP2aSdGSQ==,
        tarball: https://registry.npmjs.org/@swc/core-darwin-arm64/-/core-darwin-arm64-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [arm64]
    os: [darwin]

  '@swc/core-darwin-x64@1.11.31':
    resolution:
      {
        integrity: sha512-THSGaSwT96JwXDwuXQ6yFBbn+xDMdyw7OmBpnweAWsh5DhZmQkALEm1DgdQO3+rrE99MkmzwAfclc0UmYro/OA==,
        tarball: https://registry.npmjs.org/@swc/core-darwin-x64/-/core-darwin-x64-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [x64]
    os: [darwin]

  '@swc/core-linux-arm-gnueabihf@1.11.31':
    resolution:
      {
        integrity: sha512-laKtQFnW7KHgE57Hx32os2SNAogcuIDxYE+3DYIOmDMqD7/1DCfJe6Rln2N9WcOw6HuDbDpyQavIwZNfSAa8vQ==,
        tarball: https://registry.npmjs.org/@swc/core-linux-arm-gnueabihf/-/core-linux-arm-gnueabihf-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [arm]
    os: [linux]

  '@swc/core-linux-arm64-gnu@1.11.31':
    resolution:
      {
        integrity: sha512-T+vGw9aPE1YVyRxRr1n7NAdkbgzBzrXCCJ95xAZc/0+WUwmL77Z+js0J5v1KKTRxw4FvrslNCOXzMWrSLdwPSA==,
        tarball: https://registry.npmjs.org/@swc/core-linux-arm64-gnu/-/core-linux-arm64-gnu-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [arm64]
    os: [linux]

  '@swc/core-linux-arm64-musl@1.11.31':
    resolution:
      {
        integrity: sha512-Mztp5NZkyd5MrOAG+kl+QSn0lL4Uawd4CK4J7wm97Hs44N9DHGIG5nOz7Qve1KZo407Y25lTxi/PqzPKHo61zQ==,
        tarball: https://registry.npmjs.org/@swc/core-linux-arm64-musl/-/core-linux-arm64-musl-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [arm64]
    os: [linux]

  '@swc/core-linux-x64-gnu@1.11.31':
    resolution:
      {
        integrity: sha512-DDVE0LZcXOWwOqFU1Xi7gdtiUg3FHA0vbGb3trjWCuI1ZtDZHEQYL4M3/2FjqKZtIwASrDvO96w91okZbXhvMg==,
        tarball: https://registry.npmjs.org/@swc/core-linux-x64-gnu/-/core-linux-x64-gnu-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [x64]
    os: [linux]

  '@swc/core-linux-x64-musl@1.11.31':
    resolution:
      {
        integrity: sha512-mJA1MzPPRIfaBUHZi0xJQ4vwL09MNWDeFtxXb0r4Yzpf0v5Lue9ymumcBPmw/h6TKWms+Non4+TDquAsweuKSw==,
        tarball: https://registry.npmjs.org/@swc/core-linux-x64-musl/-/core-linux-x64-musl-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [x64]
    os: [linux]

  '@swc/core-win32-arm64-msvc@1.11.31':
    resolution:
      {
        integrity: sha512-RdtakUkNVAb/FFIMw3LnfNdlH1/ep6KgiPDRlmyUfd0WdIQ3OACmeBegEFNFTzi7gEuzy2Yxg4LWf4IUVk8/bg==,
        tarball: https://registry.npmjs.org/@swc/core-win32-arm64-msvc/-/core-win32-arm64-msvc-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [arm64]
    os: [win32]

  '@swc/core-win32-ia32-msvc@1.11.31':
    resolution:
      {
        integrity: sha512-hErXdCGsg7swWdG1fossuL8542I59xV+all751mYlBoZ8kOghLSKObGQTkBbuNvc0sUKWfWg1X0iBuIhAYar+w==,
        tarball: https://registry.npmjs.org/@swc/core-win32-ia32-msvc/-/core-win32-ia32-msvc-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [ia32]
    os: [win32]

  '@swc/core-win32-x64-msvc@1.11.31':
    resolution:
      {
        integrity: sha512-5t7SGjUBMMhF9b5j17ml/f/498kiBJNf4vZFNM421UGUEETdtjPN9jZIuQrowBkoFGJTCVL/ECM4YRtTH30u/A==,
        tarball: https://registry.npmjs.org/@swc/core-win32-x64-msvc/-/core-win32-x64-msvc-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    cpu: [x64]
    os: [win32]

  '@swc/core@1.11.31':
    resolution:
      {
        integrity: sha512-mAby9aUnKRjMEA7v8cVZS9Ah4duoRBnX7X6r5qrhTxErx+68MoY1TPrVwj/66/SWN3Bl+jijqAqoB8Qx0QE34A==,
        tarball: https://registry.npmjs.org/@swc/core/-/core-1.11.31.tgz,
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@swc/helpers': '>=0.5.17'
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true

  '@swc/counter@0.1.3':
    resolution:
      {
        integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==,
        tarball: https://registry.npmjs.org/@swc/counter/-/counter-0.1.3.tgz,
      }

  '@swc/types@0.1.21':
    resolution:
      {
        integrity: sha512-2YEtj5HJVbKivud9N4bpPBAyZhj4S2Ipe5LkUG94alTpr7in/GU/EARgPAd3BwU+YOmFVJC2+kjqhGRi3r0ZpQ==,
        tarball: https://registry.npmjs.org/@swc/types/-/types-0.1.21.tgz,
      }

  '@tanstack/eslint-plugin-query@5.78.0':
    resolution:
      {
        integrity: sha512-hYkhWr3UP0CkAsn/phBVR98UQawbw8CmTSgWtdgEBUjI60/GBaEIkpgi/Bp/2I8eIDK4+vdY7ac6jZx+GR+hEQ==,
        tarball: https://registry.npmjs.org/@tanstack/eslint-plugin-query/-/eslint-plugin-query-5.78.0.tgz,
      }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0

  '@types/apollo-upload-client@18.0.0':
    resolution:
      {
        integrity: sha512-cMgITNemktxasqvp6jiPj15dv84n3FTMvMoYBP1+xonDS+0l6JygIJrj2LJh85rShRzTOOkrElrAsCXXARa3KA==,
        tarball: https://registry.npmjs.org/@types/apollo-upload-client/-/apollo-upload-client-18.0.0.tgz,
      }

  '@types/conventional-commits-parser@5.0.1':
    resolution:
      {
        integrity: sha512-7uz5EHdzz2TqoMfV7ee61Egf5y6NkcO4FB/1iCCQnbeiI1F3xzv3vK5dBCXUCLQgGYS+mUeigK1iKQzvED+QnQ==,
        tarball: https://registry.npmjs.org/@types/conventional-commits-parser/-/conventional-commits-parser-5.0.1.tgz,
      }

  '@types/estree@1.0.7':
    resolution:
      {
        integrity: sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==,
        tarball: https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz,
      }

  '@types/extract-files@13.0.1':
    resolution:
      {
        integrity: sha512-/fRbzc2lAd7jDJSSnxWiUyXWjdUZZ4HbISLJzVgt1AvrdOa7U49YRPcvuCUywkmURZ7uwJOheDjx19itbQ5KvA==,
        tarball: https://registry.npmjs.org/@types/extract-files/-/extract-files-13.0.1.tgz,
      }

  '@types/istanbul-lib-coverage@2.0.6':
    resolution:
      {
        integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==,
        tarball: https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz,
      }

  '@types/istanbul-lib-report@3.0.3':
    resolution:
      {
        integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==,
        tarball: https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz,
      }

  '@types/istanbul-reports@3.0.4':
    resolution:
      {
        integrity: sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==,
        tarball: https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz,
      }

  '@types/jest@29.5.12':
    resolution:
      {
        integrity: sha512-eDC8bTvT/QhYdxJAulQikueigY5AsdBRH2yDKW3yveW7svY3+DzN84/2NUgkw10RTiJbWqZrTtoGVdYlvFJdLw==,
        tarball: https://registry.npmjs.org/@types/jest/-/jest-29.5.12.tgz,
      }

  '@types/js-cookie@2.2.7':
    resolution:
      {
        integrity: sha512-aLkWa0C0vO5b4Sr798E26QgOkss68Un0bLjs7u9qxzPT5CG+8DuNTffWES58YzJs3hrVAOs1wonycqEBqNJubA==,
        tarball: https://registry.npmjs.org/@types/js-cookie/-/js-cookie-2.2.7.tgz,
      }

  '@types/js-yaml@4.0.9':
    resolution:
      {
        integrity: sha512-k4MGaQl5TGo/iipqb2UDG2UwjXziSWkh0uysQelTlJpX1qGlpUZYm8PnO4DxG1qBomtJUdYJ6qR6xdIah10JLg==,
        tarball: https://registry.npmjs.org/@types/js-yaml/-/js-yaml-4.0.9.tgz,
      }

  '@types/json-schema@7.0.15':
    resolution:
      {
        integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==,
        tarball: https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz,
      }

  '@types/lodash@4.17.15':
    resolution:
      {
        integrity: sha512-w/P33JFeySuhN6JLkysYUK2gEmy9kHHFN7E8ro0tkfmlDOgxBDzWEZ/J8cWA+fHqFevpswDTFZnDx+R9lbL6xw==,
        tarball: https://registry.npmjs.org/@types/lodash/-/lodash-4.17.15.tgz,
      }

  '@types/node@20.11.30':
    resolution:
      {
        integrity: sha512-dHM6ZxwlmuZaRmUPfv1p+KrdD1Dci04FbdEm/9wEMouFqxYoFl5aMkt0VMAUtYRQDyYvD41WJLukhq/ha3YuTw==,
        tarball: https://registry.npmjs.org/@types/node/-/node-20.11.30.tgz,
      }

  '@types/prop-types@15.7.14':
    resolution:
      {
        integrity: sha512-gNMvNH49DJ7OJYv+KAKn0Xp45p8PLl6zo2YnvDIbTd4J6MER2BmWN49TG7n9LvkyihINxeKW8+3bfS2yDC9dzQ==,
        tarball: https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.14.tgz,
      }

  '@types/react-dom@18.2.22':
    resolution:
      {
        integrity: sha512-fHkBXPeNtfvri6gdsMYyW+dW7RXFo6Ad09nLFK0VQWR7yGLai/Cyvyj696gbwYvBnhGtevUG9cET0pmUbMtoPQ==,
        tarball: https://registry.npmjs.org/@types/react-dom/-/react-dom-18.2.22.tgz,
      }

  '@types/react@18.2.70':
    resolution:
      {
        integrity: sha512-hjlM2hho2vqklPhopNkXkdkeq6Lv8WSZTpr7956zY+3WS5cfYUewtCzsJLsbW5dEv3lfSeQ4W14ZFeKC437JRQ==,
        tarball: https://registry.npmjs.org/@types/react/-/react-18.2.70.tgz,
      }

  '@types/scheduler@0.26.0':
    resolution:
      {
        integrity: sha512-WFHp9YUJQ6CKshqoC37iOlHnQSmxNc795UhB26CyBBttrN9svdIrUjl/NjnNmfcwtncN0h/0PPAFWv9ovP8mLA==,
        tarball: https://registry.npmjs.org/@types/scheduler/-/scheduler-0.26.0.tgz,
      }

  '@types/stack-utils@2.0.3':
    resolution:
      {
        integrity: sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==,
        tarball: https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz,
      }

  '@types/uuid@9.0.8':
    resolution:
      {
        integrity: sha512-jg+97EGIcY9AGHJJRaaPVgetKDsrTgbRjQ5Msgjh/DQKEFl0DtyRr/VCOyD1T2R1MNeWPK/u7JoGhlDZnKBAfA==,
        tarball: https://registry.npmjs.org/@types/uuid/-/uuid-9.0.8.tgz,
      }

  '@types/ws@8.18.1':
    resolution:
      {
        integrity: sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==,
        tarball: https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz,
      }

  '@types/yargs-parser@21.0.3':
    resolution:
      {
        integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==,
        tarball: https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz,
      }

  '@types/yargs@17.0.33':
    resolution:
      {
        integrity: sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==,
        tarball: https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz,
      }

  '@typescript-eslint/eslint-plugin@8.34.0':
    resolution:
      {
        integrity: sha512-QXwAlHlbcAwNlEEMKQS2RCgJsgXrTJdjXT08xEgbPFa2yYQgVjBymxP5DrfrE7X7iodSzd9qBUHUycdyVJTW1w==,
        tarball: https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      '@typescript-eslint/parser': ^8.34.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/parser@8.34.0':
    resolution:
      {
        integrity: sha512-vxXJV1hVFx3IXz/oy2sICsJukaBrtDEQSBiV48/YIV5KWjX1dO+bcIr/kCPrW6weKXvsaGKFNlwH0v2eYdRRbA==,
        tarball: https://registry.npmjs.org/@typescript-eslint/parser/-/parser-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/project-service@8.34.0':
    resolution:
      {
        integrity: sha512-iEgDALRf970/B2YExmtPMPF54NenZUf4xpL3wsCRx/lgjz6ul/l13R81ozP/ZNuXfnLCS+oPmG7JIxfdNYKELw==,
        tarball: https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/scope-manager@8.34.0':
    resolution:
      {
        integrity: sha512-9Ac0X8WiLykl0aj1oYQNcLZjHgBojT6cW68yAgZ19letYu+Hxd0rE0veI1XznSSst1X5lwnxhPbVdwjDRIomRw==,
        tarball: https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@typescript-eslint/tsconfig-utils@8.34.0':
    resolution:
      {
        integrity: sha512-+W9VYHKFIzA5cBeooqQxqNriAP0QeQ7xTiDuIOr71hzgffm3EL2hxwWBIIj4GuofIbKxGNarpKqIq6Q6YrShOA==,
        tarball: https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/type-utils@8.34.0':
    resolution:
      {
        integrity: sha512-n7zSmOcUVhcRYC75W2pnPpbO1iwhJY3NLoHEtbJwJSNlVAZuwqu05zY3f3s2SDWWDSo9FdN5szqc73DCtDObAg==,
        tarball: https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/types@8.34.0':
    resolution:
      {
        integrity: sha512-9V24k/paICYPniajHfJ4cuAWETnt7Ssy+R0Rbcqo5sSFr3QEZ/8TSoUi9XeXVBGXCaLtwTOKSLGcInCAvyZeMA==,
        tarball: https://registry.npmjs.org/@typescript-eslint/types/-/types-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@typescript-eslint/typescript-estree@8.34.0':
    resolution:
      {
        integrity: sha512-rOi4KZxI7E0+BMqG7emPSK1bB4RICCpF7QD3KCLXn9ZvWoESsOMlHyZPAHyG04ujVplPaHbmEvs34m+wjgtVtg==,
        tarball: https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/utils@8.34.0':
    resolution:
      {
        integrity: sha512-8L4tWatGchV9A1cKbjaavS6mwYwp39jql8xUmIIKJdm+qiaeHy5KMKlBrf30akXAWBzn2SqKsNOtSENWUwg7XQ==,
        tarball: https://registry.npmjs.org/@typescript-eslint/utils/-/utils-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/visitor-keys@8.34.0':
    resolution:
      {
        integrity: sha512-qHV7pW7E85A0x6qyrFn+O+q1k1p3tQCsqIZ1KZ5ESLXY57aTvUd3/a4rdPTeXisvhXn2VQG0VSKUqs8KHF2zcA==,
        tarball: https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-8.34.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  '@vitejs/plugin-react-swc@3.7.2':
    resolution:
      {
        integrity: sha512-y0byko2b2tSVVf5Gpng1eEhX1OvPC7x8yns1Fx8jDzlJp4LS6CMkCPfLw47cjyoMrshQDoQw4qcgjsU9VvlCew==,
        tarball: https://registry.npmjs.org/@vitejs/plugin-react-swc/-/plugin-react-swc-3.7.2.tgz,
      }
    peerDependencies:
      vite: ^4 || ^5 || ^6

  '@whatwg-node/disposablestack@0.0.6':
    resolution:
      {
        integrity: sha512-LOtTn+JgJvX8WfBVJtF08TGrdjuFzGJc4mkP8EdDI8ADbvO7kiexYep1o8dwnt0okb0jYclCDXF13xU7Ge4zSw==,
        tarball: https://registry.npmjs.org/@whatwg-node/disposablestack/-/disposablestack-0.0.6.tgz,
      }
    engines: { node: '>=18.0.0' }

  '@whatwg-node/events@0.0.3':
    resolution:
      {
        integrity: sha512-IqnKIDWfXBJkvy/k6tzskWTc2NK3LcqHlb+KHGCrjOCH4jfQckRX0NAiIcC/vIqQkzLYw2r2CTSwAxcrtcD6lA==,
        tarball: https://registry.npmjs.org/@whatwg-node/events/-/events-0.0.3.tgz,
      }

  '@whatwg-node/fetch@0.10.8':
    resolution:
      {
        integrity: sha512-Rw9z3ctmeEj8QIB9MavkNJqekiu9usBCSMZa+uuAvM0lF3v70oQVCXNppMIqaV6OTZbdaHF1M2HLow58DEw+wg==,
        tarball: https://registry.npmjs.org/@whatwg-node/fetch/-/fetch-0.10.8.tgz,
      }
    engines: { node: '>=18.0.0' }

  '@whatwg-node/fetch@0.8.8':
    resolution:
      {
        integrity: sha512-CdcjGC2vdKhc13KKxgsc6/616BQ7ooDIgPeTuAiE8qfCnS0mGzcfCOoZXypQSz73nxI+GWc7ZReIAVhxoE1KCg==,
        tarball: https://registry.npmjs.org/@whatwg-node/fetch/-/fetch-0.8.8.tgz,
      }

  '@whatwg-node/node-fetch@0.3.6':
    resolution:
      {
        integrity: sha512-w9wKgDO4C95qnXZRwZTfCmLWqyRnooGjcIwG0wADWjw9/HN0p7dtvtgSvItZtUyNteEvgTrd8QojNEqV6DAGTA==,
        tarball: https://registry.npmjs.org/@whatwg-node/node-fetch/-/node-fetch-0.3.6.tgz,
      }

  '@whatwg-node/node-fetch@0.7.21':
    resolution:
      {
        integrity: sha512-QC16IdsEyIW7kZd77aodrMO7zAoDyyqRCTLg+qG4wqtP4JV9AA+p7/lgqMdD29XyiYdVvIdFrfI9yh7B1QvRvw==,
        tarball: https://registry.npmjs.org/@whatwg-node/node-fetch/-/node-fetch-0.7.21.tgz,
      }
    engines: { node: '>=18.0.0' }

  '@whatwg-node/promise-helpers@1.3.2':
    resolution:
      {
        integrity: sha512-Nst5JdK47VIl9UcGwtv2Rcgyn5lWtZ0/mhRQ4G8NN2isxpq2TO30iqHzmwoJycjWuyUfg3GFXqP/gFHXeV57IA==,
        tarball: https://registry.npmjs.org/@whatwg-node/promise-helpers/-/promise-helpers-1.3.2.tgz,
      }
    engines: { node: '>=16.0.0' }

  '@wry/caches@1.0.1':
    resolution:
      {
        integrity: sha512-bXuaUNLVVkD20wcGBWRyo7j9N3TxePEWFZj2Y+r9OoUzfqmavM84+mFykRicNsBqatba5JLay1t48wxaXaWnlA==,
        tarball: https://registry.npmjs.org/@wry/caches/-/caches-1.0.1.tgz,
      }
    engines: { node: '>=8' }

  '@wry/context@0.7.4':
    resolution:
      {
        integrity: sha512-jmT7Sb4ZQWI5iyu3lobQxICu2nC/vbUhP0vIdd6tHC9PTfenmRmuIFqktc6GH9cgi+ZHnsLWPvfSvc4DrYmKiQ==,
        tarball: https://registry.npmjs.org/@wry/context/-/context-0.7.4.tgz,
      }
    engines: { node: '>=8' }

  '@wry/equality@0.5.7':
    resolution:
      {
        integrity: sha512-BRFORjsTuQv5gxcXsuDXx6oGRhuVsEGwZy6LOzRRfgu+eSfxbhUQ9L9YtSEIuIjY/o7g3iWFjrc5eSY1GXP2Dw==,
        tarball: https://registry.npmjs.org/@wry/equality/-/equality-0.5.7.tgz,
      }
    engines: { node: '>=8' }

  '@wry/trie@0.5.0':
    resolution:
      {
        integrity: sha512-FNoYzHawTMk/6KMQoEG5O4PuioX19UbwdQKF44yw0nLfOypfQdjtfZzo/UIJWAJ23sNIFbD1Ug9lbaDGMwbqQA==,
        tarball: https://registry.npmjs.org/@wry/trie/-/trie-0.5.0.tgz,
      }
    engines: { node: '>=8' }

  '@xobotyi/scrollbar-width@1.9.5':
    resolution:
      {
        integrity: sha512-N8tkAACJx2ww8vFMneJmaAgmjAG1tnVBZJRLRcx061tmsLRZHSEZSLuGWnwPtunsSLvSqXQ2wfp7Mgqg1I+2dQ==,
        tarball: https://registry.npmjs.org/@xobotyi/scrollbar-width/-/scrollbar-width-1.9.5.tgz,
      }

  JSONStream@1.3.5:
    resolution:
      {
        integrity: sha512-E+iruNOY8VV9s4JEbe1aNEm6MiszPRr/UfcHMz0TQh1BXSxHK+ASV1R6W4HpjBhSeS+54PIsAMCBmwD06LLsqQ==,
        tarball: https://registry.npmjs.org/JSONStream/-/JSONStream-1.3.5.tgz,
      }
    hasBin: true

  acorn-jsx@5.3.2:
    resolution:
      {
        integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==,
        tarball: https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz,
      }
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.14.1:
    resolution:
      {
        integrity: sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==,
        tarball: https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz,
      }
    engines: { node: '>=0.4.0' }
    hasBin: true

  acorn@8.15.0:
    resolution:
      {
        integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==,
        tarball: https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz,
      }
    engines: { node: '>=0.4.0' }
    hasBin: true

  agent-base@6.0.2:
    resolution:
      {
        integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==,
        tarball: https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz,
      }
    engines: { node: '>= 6.0.0' }

  agent-base@7.1.3:
    resolution:
      {
        integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==,
        tarball: https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz,
      }
    engines: { node: '>= 14' }

  aggregate-error@3.1.0:
    resolution:
      {
        integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==,
        tarball: https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz,
      }
    engines: { node: '>=8' }

  ajv@6.12.6:
    resolution:
      {
        integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==,
        tarball: https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz,
      }

  ajv@8.17.1:
    resolution:
      {
        integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==,
        tarball: https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz,
      }

  ansi-escapes@4.3.2:
    resolution:
      {
        integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==,
        tarball: https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz,
      }
    engines: { node: '>=8' }

  ansi-regex@5.0.1:
    resolution:
      {
        integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==,
        tarball: https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz,
      }
    engines: { node: '>=8' }

  ansi-regex@6.1.0:
    resolution:
      {
        integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==,
        tarball: https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz,
      }
    engines: { node: '>=12' }

  ansi-styles@4.3.0:
    resolution:
      {
        integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==,
        tarball: https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz,
      }
    engines: { node: '>=8' }

  ansi-styles@5.2.0:
    resolution:
      {
        integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==,
        tarball: https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz,
      }
    engines: { node: '>=10' }

  ansi-styles@6.2.1:
    resolution:
      {
        integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==,
        tarball: https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz,
      }
    engines: { node: '>=12' }

  any-promise@1.3.0:
    resolution:
      {
        integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==,
        tarball: https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz,
      }

  anymatch@3.1.3:
    resolution:
      {
        integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==,
        tarball: https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz,
      }
    engines: { node: '>= 8' }

  apollo-upload-client@18.0.1:
    resolution:
      {
        integrity: sha512-OQvZg1rK05VNI79D658FUmMdoI2oB/KJKb6QGMa2Si25QXOaAvLMBFUEwJct7wf+19U8vk9ILhidBOU1ZWv6QA==,
        tarball: https://registry.npmjs.org/apollo-upload-client/-/apollo-upload-client-18.0.1.tgz,
      }
    engines: { node: ^18.15.0 || >=20.4.0 }
    peerDependencies:
      '@apollo/client': ^3.8.0
      graphql: 14 - 16

  arg@5.0.2:
    resolution:
      {
        integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==,
        tarball: https://registry.npmjs.org/arg/-/arg-5.0.2.tgz,
      }

  argparse@2.0.1:
    resolution:
      {
        integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==,
        tarball: https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz,
      }

  aria-hidden@1.2.6:
    resolution:
      {
        integrity: sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA==,
        tarball: https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.6.tgz,
      }
    engines: { node: '>=10' }

  array-buffer-byte-length@1.0.2:
    resolution:
      {
        integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==,
        tarball: https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  array-ify@1.0.0:
    resolution:
      {
        integrity: sha512-c5AMf34bKdvPhQ7tBGhqkgKNUzMr4WUs+WDtC2ZUGOUncbxKMTvqxYctiseW3+L4bA8ec+GcZ6/A/FW4m8ukng==,
        tarball: https://registry.npmjs.org/array-ify/-/array-ify-1.0.0.tgz,
      }

  array-includes@3.1.9:
    resolution:
      {
        integrity: sha512-FmeCCAenzH0KH381SPT5FZmiA/TmpndpcaShhfgEN9eCVjnFBqq3l1xrI42y8+PPLI6hypzou4GXw00WHmPBLQ==,
        tarball: https://registry.npmjs.org/array-includes/-/array-includes-3.1.9.tgz,
      }
    engines: { node: '>= 0.4' }

  array-union@2.1.0:
    resolution:
      {
        integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==,
        tarball: https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz,
      }
    engines: { node: '>=8' }

  array.prototype.findlast@1.2.5:
    resolution:
      {
        integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==,
        tarball: https://registry.npmjs.org/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz,
      }
    engines: { node: '>= 0.4' }

  array.prototype.flat@1.3.3:
    resolution:
      {
        integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==,
        tarball: https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz,
      }
    engines: { node: '>= 0.4' }

  array.prototype.flatmap@1.3.3:
    resolution:
      {
        integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==,
        tarball: https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz,
      }
    engines: { node: '>= 0.4' }

  array.prototype.tosorted@1.1.4:
    resolution:
      {
        integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==,
        tarball: https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz,
      }
    engines: { node: '>= 0.4' }

  arraybuffer.prototype.slice@1.0.4:
    resolution:
      {
        integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==,
        tarball: https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz,
      }
    engines: { node: '>= 0.4' }

  asap@2.0.6:
    resolution:
      {
        integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==,
        tarball: https://registry.npmjs.org/asap/-/asap-2.0.6.tgz,
      }

  asn1js@3.0.6:
    resolution:
      {
        integrity: sha512-UOCGPYbl0tv8+006qks/dTgV9ajs97X2p0FAbyS2iyCRrmLSRolDaHdp+v/CLgnzHc3fVB+CwYiUmei7ndFcgA==,
        tarball: https://registry.npmjs.org/asn1js/-/asn1js-3.0.6.tgz,
      }
    engines: { node: '>=12.0.0' }

  astral-regex@2.0.0:
    resolution:
      {
        integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==,
        tarball: https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz,
      }
    engines: { node: '>=8' }

  async-function@1.0.0:
    resolution:
      {
        integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==,
        tarball: https://registry.npmjs.org/async-function/-/async-function-1.0.0.tgz,
      }
    engines: { node: '>= 0.4' }

  asynckit@0.4.0:
    resolution:
      {
        integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==,
        tarball: https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz,
      }

  auto-bind@4.0.0:
    resolution:
      {
        integrity: sha512-Hdw8qdNiqdJ8LqT0iK0sVzkFbzg6fhnQqqfWhBDxcHZvU75+B+ayzTy8x+k5Ix0Y92XOhOUlx74ps+bA6BeYMQ==,
        tarball: https://registry.npmjs.org/auto-bind/-/auto-bind-4.0.0.tgz,
      }
    engines: { node: '>=8' }

  autoprefixer@10.4.20:
    resolution:
      {
        integrity: sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==,
        tarball: https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.20.tgz,
      }
    engines: { node: ^10 || ^12 || >=14 }
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  available-typed-arrays@1.0.7:
    resolution:
      {
        integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==,
        tarball: https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz,
      }
    engines: { node: '>= 0.4' }

  axios@1.9.0:
    resolution:
      {
        integrity: sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==,
        tarball: https://registry.npmjs.org/axios/-/axios-1.9.0.tgz,
      }

  babel-plugin-syntax-trailing-function-commas@7.0.0-beta.0:
    resolution:
      {
        integrity: sha512-Xj9XuRuz3nTSbaTXWv3itLOcxyF4oPD8douBBmj7U9BBC6nEBYfyOJYQMf/8PJAFotC62UY5dFfIGEPr7WswzQ==,
        tarball: https://registry.npmjs.org/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-7.0.0-beta.0.tgz,
      }

  babel-preset-fbjs@3.4.0:
    resolution:
      {
        integrity: sha512-9ywCsCvo1ojrw0b+XYk7aFvTH6D9064t0RIL1rtMf3nsa02Xw41MS7sZw216Im35xj/UY0PDBQsa1brUDDF1Ow==,
        tarball: https://registry.npmjs.org/babel-preset-fbjs/-/babel-preset-fbjs-3.4.0.tgz,
      }
    peerDependencies:
      '@babel/core': ^7.0.0

  balanced-match@1.0.2:
    resolution:
      {
        integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==,
        tarball: https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz,
      }

  base64-js@1.5.1:
    resolution:
      {
        integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==,
        tarball: https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz,
      }

  binary-extensions@2.3.0:
    resolution:
      {
        integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==,
        tarball: https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz,
      }
    engines: { node: '>=8' }

  bl@4.1.0:
    resolution:
      {
        integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==,
        tarball: https://registry.npmjs.org/bl/-/bl-4.1.0.tgz,
      }

  brace-expansion@1.1.11:
    resolution:
      {
        integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==,
        tarball: https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz,
      }

  brace-expansion@2.0.1:
    resolution:
      {
        integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==,
        tarball: https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz,
      }

  braces@3.0.3:
    resolution:
      {
        integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==,
        tarball: https://registry.npmjs.org/braces/-/braces-3.0.3.tgz,
      }
    engines: { node: '>=8' }

  browserslist@4.25.0:
    resolution:
      {
        integrity: sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==,
        tarball: https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz,
      }
    engines: { node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7 }
    hasBin: true

  bser@2.1.1:
    resolution:
      {
        integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==,
        tarball: https://registry.npmjs.org/bser/-/bser-2.1.1.tgz,
      }

  buffer@5.7.1:
    resolution:
      {
        integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==,
        tarball: https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz,
      }

  buffer@6.0.3:
    resolution:
      {
        integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==,
        tarball: https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz,
      }

  busboy@1.6.0:
    resolution:
      {
        integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==,
        tarball: https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz,
      }
    engines: { node: '>=10.16.0' }

  cacheable@1.9.0:
    resolution:
      {
        integrity: sha512-8D5htMCxPDUULux9gFzv30f04Xo3wCnik0oOxKoRTPIBoqA7HtOcJ87uBhQTs3jCfZZTrUBGsYIZOgE0ZRgMAg==,
        tarball: https://registry.npmjs.org/cacheable/-/cacheable-1.9.0.tgz,
      }

  call-bind-apply-helpers@1.0.2:
    resolution:
      {
        integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==,
        tarball: https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  call-bind@1.0.8:
    resolution:
      {
        integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==,
        tarball: https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz,
      }
    engines: { node: '>= 0.4' }

  call-bound@1.0.4:
    resolution:
      {
        integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==,
        tarball: https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz,
      }
    engines: { node: '>= 0.4' }

  callsites@3.1.0:
    resolution:
      {
        integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==,
        tarball: https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz,
      }
    engines: { node: '>=6' }

  camel-case@4.1.2:
    resolution:
      {
        integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==,
        tarball: https://registry.npmjs.org/camel-case/-/camel-case-4.1.2.tgz,
      }

  camelcase-css@2.0.1:
    resolution:
      {
        integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==,
        tarball: https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz,
      }
    engines: { node: '>= 6' }

  camelcase@5.3.1:
    resolution:
      {
        integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==,
        tarball: https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz,
      }
    engines: { node: '>=6' }

  camelcase@6.3.0:
    resolution:
      {
        integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==,
        tarball: https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz,
      }
    engines: { node: '>=10' }

  caniuse-lite@1.0.30001721:
    resolution:
      {
        integrity: sha512-cOuvmUVtKrtEaoKiO0rSc29jcjwMwX5tOHDy4MgVFEWiUXj4uBMJkwI8MDySkgXidpMiHUcviogAvFi4pA2hDQ==,
        tarball: https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001721.tgz,
      }

  capital-case@1.0.4:
    resolution:
      {
        integrity: sha512-ds37W8CytHgwnhGGTi88pcPyR15qoNkOpYwmMMfnWqqWgESapLqvDx6huFjQ5vqWSn2Z06173XNA7LtMOeUh1A==,
        tarball: https://registry.npmjs.org/capital-case/-/capital-case-1.0.4.tgz,
      }

  chalk@4.1.2:
    resolution:
      {
        integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==,
        tarball: https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz,
      }
    engines: { node: '>=10' }

  chalk@5.4.1:
    resolution:
      {
        integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==,
        tarball: https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz,
      }
    engines: { node: ^12.17.0 || ^14.13 || >=16.0.0 }

  change-case-all@1.0.14:
    resolution:
      {
        integrity: sha512-CWVm2uT7dmSHdO/z1CXT/n47mWonyypzBbuCy5tN7uMg22BsfkhwT6oHmFCAk+gL1LOOxhdbB9SZz3J1KTY3gA==,
        tarball: https://registry.npmjs.org/change-case-all/-/change-case-all-1.0.14.tgz,
      }

  change-case-all@1.0.15:
    resolution:
      {
        integrity: sha512-3+GIFhk3sNuvFAJKU46o26OdzudQlPNBCu1ZQi3cMeMHhty1bhDxu2WrEilVNYaGvqUtR1VSigFcJOiS13dRhQ==,
        tarball: https://registry.npmjs.org/change-case-all/-/change-case-all-1.0.15.tgz,
      }

  change-case@4.1.2:
    resolution:
      {
        integrity: sha512-bSxY2ws9OtviILG1EiY5K7NNxkqg/JnRnFxLtKQ96JaviiIxi7djMrSd0ECT9AC+lttClmYwKw53BWpOMblo7A==,
        tarball: https://registry.npmjs.org/change-case/-/change-case-4.1.2.tgz,
      }

  chardet@0.7.0:
    resolution:
      {
        integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==,
        tarball: https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz,
      }

  chokidar@3.6.0:
    resolution:
      {
        integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==,
        tarball: https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz,
      }
    engines: { node: '>= 8.10.0' }

  ci-info@3.9.0:
    resolution:
      {
        integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==,
        tarball: https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz,
      }
    engines: { node: '>=8' }

  class-variance-authority@0.7.1:
    resolution:
      {
        integrity: sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==,
        tarball: https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz,
      }

  clean-css@5.3.3:
    resolution:
      {
        integrity: sha512-D5J+kHaVb/wKSFcyyV75uCn8fiY4sV38XJoe4CUyGQ+mOU/fMVYUdH1hJC+CJQ5uY3EnW27SbJYS4X8BiLrAFg==,
        tarball: https://registry.npmjs.org/clean-css/-/clean-css-5.3.3.tgz,
      }
    engines: { node: '>= 10.0' }

  clean-stack@2.2.0:
    resolution:
      {
        integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==,
        tarball: https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz,
      }
    engines: { node: '>=6' }

  cli-cursor@3.1.0:
    resolution:
      {
        integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==,
        tarball: https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz,
      }
    engines: { node: '>=8' }

  cli-spinners@2.9.2:
    resolution:
      {
        integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==,
        tarball: https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz,
      }
    engines: { node: '>=6' }

  cli-truncate@2.1.0:
    resolution:
      {
        integrity: sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg==,
        tarball: https://registry.npmjs.org/cli-truncate/-/cli-truncate-2.1.0.tgz,
      }
    engines: { node: '>=8' }

  cli-width@3.0.0:
    resolution:
      {
        integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==,
        tarball: https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz,
      }
    engines: { node: '>= 10' }

  cliui@6.0.0:
    resolution:
      {
        integrity: sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==,
        tarball: https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz,
      }

  cliui@8.0.1:
    resolution:
      {
        integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==,
        tarball: https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz,
      }
    engines: { node: '>=12' }

  clone@1.0.4:
    resolution:
      {
        integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==,
        tarball: https://registry.npmjs.org/clone/-/clone-1.0.4.tgz,
      }
    engines: { node: '>=0.8' }

  clsx@2.1.1:
    resolution:
      {
        integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==,
        tarball: https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz,
      }
    engines: { node: '>=6' }

  color-convert@2.0.1:
    resolution:
      {
        integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==,
        tarball: https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz,
      }
    engines: { node: '>=7.0.0' }

  color-name@1.1.4:
    resolution:
      {
        integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==,
        tarball: https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz,
      }

  colorette@2.0.20:
    resolution:
      {
        integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==,
        tarball: https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz,
      }

  combined-stream@1.0.8:
    resolution:
      {
        integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==,
        tarball: https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz,
      }
    engines: { node: '>= 0.8' }

  commander@4.1.1:
    resolution:
      {
        integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==,
        tarball: https://registry.npmjs.org/commander/-/commander-4.1.1.tgz,
      }
    engines: { node: '>= 6' }

  common-tags@1.8.2:
    resolution:
      {
        integrity: sha512-gk/Z852D2Wtb//0I+kRFNKKE9dIIVirjoqPoA1wJU+XePVXZfGeBpk45+A1rKO4Q43prqWBNY/MiIeRLbPWUaA==,
        tarball: https://registry.npmjs.org/common-tags/-/common-tags-1.8.2.tgz,
      }
    engines: { node: '>=4.0.0' }

  compare-func@2.0.0:
    resolution:
      {
        integrity: sha512-zHig5N+tPWARooBnb0Zx1MFcdfpyJrfTJ3Y5L+IFvUm8rM74hHz66z0gw0x4tijh5CorKkKUCnW82R2vmpeCRA==,
        tarball: https://registry.npmjs.org/compare-func/-/compare-func-2.0.0.tgz,
      }

  concat-map@0.0.1:
    resolution:
      {
        integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==,
        tarball: https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz,
      }

  constant-case@3.0.4:
    resolution:
      {
        integrity: sha512-I2hSBi7Vvs7BEuJDr5dDHfzb/Ruj3FyvFyh7KLilAjNQw3Be+xgqUBA2W6scVEcL0hL1dwPRtIqEPVUCKkSsyQ==,
        tarball: https://registry.npmjs.org/constant-case/-/constant-case-3.0.4.tgz,
      }

  conventional-changelog-angular@7.0.0:
    resolution:
      {
        integrity: sha512-ROjNchA9LgfNMTTFSIWPzebCwOGFdgkEq45EnvvrmSLvCtAw0HSmrCs7/ty+wAeYUZyNay0YMUNYFTRL72PkBQ==,
        tarball: https://registry.npmjs.org/conventional-changelog-angular/-/conventional-changelog-angular-7.0.0.tgz,
      }
    engines: { node: '>=16' }

  conventional-changelog-conventionalcommits@7.0.2:
    resolution:
      {
        integrity: sha512-NKXYmMR/Hr1DevQegFB4MwfM5Vv0m4UIxKZTTYuD98lpTknaZlSRrDOG4X7wIXpGkfsYxZTghUN+Qq+T0YQI7w==,
        tarball: https://registry.npmjs.org/conventional-changelog-conventionalcommits/-/conventional-changelog-conventionalcommits-7.0.2.tgz,
      }
    engines: { node: '>=16' }

  conventional-commits-parser@5.0.0:
    resolution:
      {
        integrity: sha512-ZPMl0ZJbw74iS9LuX9YIAiW8pfM5p3yh2o/NbXHbkFuZzY5jvdi5jFycEOkmBW5H5I7nA+D6f3UcsCLP2vvSEA==,
        tarball: https://registry.npmjs.org/conventional-commits-parser/-/conventional-commits-parser-5.0.0.tgz,
      }
    engines: { node: '>=16' }
    hasBin: true

  convert-source-map@2.0.0:
    resolution:
      {
        integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==,
        tarball: https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz,
      }

  copy-to-clipboard@3.3.3:
    resolution:
      {
        integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==,
        tarball: https://registry.npmjs.org/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz,
      }

  core-js@3.43.0:
    resolution:
      {
        integrity: sha512-N6wEbTTZSYOY2rYAn85CuvWWkCK6QweMn7/4Nr3w+gDBeBhk/x4EJeY6FPo4QzDoJZxVTv8U7CMvgWk6pOHHqA==,
        tarball: https://registry.npmjs.org/core-js/-/core-js-3.43.0.tgz,
      }

  cosmiconfig-typescript-loader@6.1.0:
    resolution:
      {
        integrity: sha512-tJ1w35ZRUiM5FeTzT7DtYWAFFv37ZLqSRkGi2oeCK1gPhvaWjkAtfXvLmvE1pRfxxp9aQo6ba/Pvg1dKj05D4g==,
        tarball: https://registry.npmjs.org/cosmiconfig-typescript-loader/-/cosmiconfig-typescript-loader-6.1.0.tgz,
      }
    engines: { node: '>=v18' }
    peerDependencies:
      '@types/node': '*'
      cosmiconfig: '>=9'
      typescript: '>=5'

  cosmiconfig@8.3.6:
    resolution:
      {
        integrity: sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==,
        tarball: https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.6.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true

  cosmiconfig@9.0.0:
    resolution:
      {
        integrity: sha512-itvL5h8RETACmOTFc4UfIyB2RfEHi71Ax6E/PivVxq9NseKbOWpeyHEOIbmAw1rs8Ak0VursQNww7lf7YtUwzg==,
        tarball: https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0.tgz,
      }
    engines: { node: '>=14' }
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true

  cross-fetch@3.2.0:
    resolution:
      {
        integrity: sha512-Q+xVJLoGOeIMXZmbUK4HYk+69cQH6LudR0Vu/pRm2YlU/hDV9CiS0gKUMaWY5f2NeUH9C1nV3bsTlCo0FsTV1Q==,
        tarball: https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.2.0.tgz,
      }

  cross-fetch@4.0.0:
    resolution:
      {
        integrity: sha512-e4a5N8lVvuLgAWgnCrLr2PP0YyDOTHa9H/Rj54dirp61qXnNq46m82bRhNqIA5VccJtWBvPTFRV3TtvHUKPB1g==,
        tarball: https://registry.npmjs.org/cross-fetch/-/cross-fetch-4.0.0.tgz,
      }

  cross-inspect@1.0.1:
    resolution:
      {
        integrity: sha512-Pcw1JTvZLSJH83iiGWt6fRcT+BjZlCDRVwYLbUcHzv/CRpB7r0MlSrGbIyQvVSNyGnbt7G4AXuyCiDR3POvZ1A==,
        tarball: https://registry.npmjs.org/cross-inspect/-/cross-inspect-1.0.1.tgz,
      }
    engines: { node: '>=16.0.0' }

  cross-spawn@7.0.6:
    resolution:
      {
        integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==,
        tarball: https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz,
      }
    engines: { node: '>= 8' }

  css-in-js-utils@3.1.0:
    resolution:
      {
        integrity: sha512-fJAcud6B3rRu+KHYk+Bwf+WFL2MDCJJ1XG9x137tJQ0xYxor7XziQtuGFbWNdqrvF4Tk26O3H73nfVqXt/fW1A==,
        tarball: https://registry.npmjs.org/css-in-js-utils/-/css-in-js-utils-3.1.0.tgz,
      }

  css-tree@1.1.3:
    resolution:
      {
        integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==,
        tarball: https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz,
      }
    engines: { node: '>=8.0.0' }

  cssesc@3.0.0:
    resolution:
      {
        integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==,
        tarball: https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz,
      }
    engines: { node: '>=4' }
    hasBin: true

  csstype@3.1.3:
    resolution:
      {
        integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==,
        tarball: https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz,
      }

  dargs@8.1.0:
    resolution:
      {
        integrity: sha512-wAV9QHOsNbwnWdNW2FYvE1P56wtgSbM+3SZcdGiWQILwVjACCXDCI3Ai8QlCjMDB8YK5zySiXZYBiwGmNY3lnw==,
        tarball: https://registry.npmjs.org/dargs/-/dargs-8.1.0.tgz,
      }
    engines: { node: '>=12' }

  data-uri-to-buffer@4.0.1:
    resolution:
      {
        integrity: sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==,
        tarball: https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz,
      }
    engines: { node: '>= 12' }

  data-view-buffer@1.0.2:
    resolution:
      {
        integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==,
        tarball: https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  data-view-byte-length@1.0.2:
    resolution:
      {
        integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==,
        tarball: https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  data-view-byte-offset@1.0.1:
    resolution:
      {
        integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==,
        tarball: https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz,
      }
    engines: { node: '>= 0.4' }

  dataloader@2.2.3:
    resolution:
      {
        integrity: sha512-y2krtASINtPFS1rSDjacrFgn1dcUuoREVabwlOGOe4SdxenREqwjwjElAdwvbGM7kgZz9a3KVicWR7vcz8rnzA==,
        tarball: https://registry.npmjs.org/dataloader/-/dataloader-2.2.3.tgz,
      }

  date-fns@3.6.0:
    resolution:
      {
        integrity: sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==,
        tarball: https://registry.npmjs.org/date-fns/-/date-fns-3.6.0.tgz,
      }

  debounce@1.2.1:
    resolution:
      {
        integrity: sha512-XRRe6Glud4rd/ZGQfiV1ruXSfbvfJedlV9Y6zOlP+2K04vBYiJEte6stfFkCP03aMnY5tsipamumUjL14fofug==,
        tarball: https://registry.npmjs.org/debounce/-/debounce-1.2.1.tgz,
      }

  debug@4.4.1:
    resolution:
      {
        integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==,
        tarball: https://registry.npmjs.org/debug/-/debug-4.4.1.tgz,
      }
    engines: { node: '>=6.0' }
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution:
      {
        integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==,
        tarball: https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  deep-is@0.1.4:
    resolution:
      {
        integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==,
        tarball: https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz,
      }

  defaults@1.0.4:
    resolution:
      {
        integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==,
        tarball: https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz,
      }

  define-data-property@1.1.4:
    resolution:
      {
        integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==,
        tarball: https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz,
      }
    engines: { node: '>= 0.4' }

  define-properties@1.2.1:
    resolution:
      {
        integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==,
        tarball: https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz,
      }
    engines: { node: '>= 0.4' }

  delayed-stream@1.0.0:
    resolution:
      {
        integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==,
        tarball: https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz,
      }
    engines: { node: '>=0.4.0' }

  dependency-graph@0.11.0:
    resolution:
      {
        integrity: sha512-JeMq7fEshyepOWDfcfHK06N3MhyPhz++vtqWhMT5O9A3K42rdsEDpfdVqjaqaAhsw6a+ZqeDvQVtD0hFHQWrzg==,
        tarball: https://registry.npmjs.org/dependency-graph/-/dependency-graph-0.11.0.tgz,
      }
    engines: { node: '>= 0.6.0' }

  detect-indent@6.1.0:
    resolution:
      {
        integrity: sha512-reYkTUJAZb9gUuZ2RvVCNhVHdg62RHnJ7WJl8ftMi4diZ6NWlciOzQN88pUhSELEwflJht4oQDv0F0BMlwaYtA==,
        tarball: https://registry.npmjs.org/detect-indent/-/detect-indent-6.1.0.tgz,
      }
    engines: { node: '>=8' }

  detect-node-es@1.1.0:
    resolution:
      {
        integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==,
        tarball: https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz,
      }

  didyoumean@1.2.2:
    resolution:
      {
        integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==,
        tarball: https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz,
      }

  diff-sequences@29.6.3:
    resolution:
      {
        integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==,
        tarball: https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  dir-glob@3.0.1:
    resolution:
      {
        integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==,
        tarball: https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz,
      }
    engines: { node: '>=8' }

  dlv@1.1.3:
    resolution:
      {
        integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==,
        tarball: https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz,
      }

  doctrine@2.1.0:
    resolution:
      {
        integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==,
        tarball: https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  dot-case@3.0.4:
    resolution:
      {
        integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==,
        tarball: https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz,
      }

  dot-prop@5.3.0:
    resolution:
      {
        integrity: sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==,
        tarball: https://registry.npmjs.org/dot-prop/-/dot-prop-5.3.0.tgz,
      }
    engines: { node: '>=8' }

  dotenv@16.5.0:
    resolution:
      {
        integrity: sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==,
        tarball: https://registry.npmjs.org/dotenv/-/dotenv-16.5.0.tgz,
      }
    engines: { node: '>=12' }

  dset@3.1.4:
    resolution:
      {
        integrity: sha512-2QF/g9/zTaPDc3BjNcVTGoBbXBgYfMTTceLaYcFJ/W9kggFUkhxD/hMEeuLKbugyef9SqAx8cpgwlIP/jinUTA==,
        tarball: https://registry.npmjs.org/dset/-/dset-3.1.4.tgz,
      }
    engines: { node: '>=4' }

  dunder-proto@1.0.1:
    resolution:
      {
        integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==,
        tarball: https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz,
      }
    engines: { node: '>= 0.4' }

  eastasianwidth@0.2.0:
    resolution:
      {
        integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==,
        tarball: https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz,
      }

  electron-to-chromium@1.5.165:
    resolution:
      {
        integrity: sha512-naiMx1Z6Nb2TxPU6fiFrUrDTjyPMLdTtaOd2oLmG8zVSg2hCWGkhPyxwk+qRmZ1ytwVqUv0u7ZcDA5+ALhaUtw==,
        tarball: https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.165.tgz,
      }

  embla-carousel-autoplay@8.5.2:
    resolution:
      {
        integrity: sha512-27emJ0px3q/c0kCHCjwRrEbYcyYUPfGO3g5IBWF1i7714TTzE6L9P81V6PHLoSMAKJ1aHoT2e7YFOsuFKCbyag==,
        tarball: https://registry.npmjs.org/embla-carousel-autoplay/-/embla-carousel-autoplay-8.5.2.tgz,
      }
    peerDependencies:
      embla-carousel: 8.5.2

  embla-carousel-fade@8.5.2:
    resolution:
      {
        integrity: sha512-QJ46Xy+mpijjquQeIY0d0sPSy34XduREUnz7tn1K20hcKyZYTONNIXQZu3GGNwG59cvhMqYJMw9ki92Rjd14YA==,
        tarball: https://registry.npmjs.org/embla-carousel-fade/-/embla-carousel-fade-8.5.2.tgz,
      }
    peerDependencies:
      embla-carousel: 8.5.2

  embla-carousel-react@8.5.2:
    resolution:
      {
        integrity: sha512-Tmx+uY3MqseIGdwp0ScyUuxpBgx5jX1f7od4Cm5mDwg/dptEiTKf9xp6tw0lZN2VA9JbnVMl/aikmbc53c6QFA==,
        tarball: https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.5.2.tgz,
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  embla-carousel-reactive-utils@8.5.2:
    resolution:
      {
        integrity: sha512-QC8/hYSK/pEmqEdU1IO5O+XNc/Ptmmq7uCB44vKplgLKhB/l0+yvYx0+Cv0sF6Ena8Srld5vUErZkT+yTahtDg==,
        tarball: https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.5.2.tgz,
      }
    peerDependencies:
      embla-carousel: 8.5.2

  embla-carousel@8.5.2:
    resolution:
      {
        integrity: sha512-xQ9oVLrun/eCG/7ru3R+I5bJ7shsD8fFwLEY7yPe27/+fDHCNj0OT5EoG5ZbFyOxOcG6yTwW8oTz/dWyFnyGpg==,
        tarball: https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.5.2.tgz,
      }

  emoji-regex@8.0.0:
    resolution:
      {
        integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==,
        tarball: https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz,
      }

  emoji-regex@9.2.2:
    resolution:
      {
        integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==,
        tarball: https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz,
      }

  entities@4.5.0:
    resolution:
      {
        integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==,
        tarball: https://registry.npmjs.org/entities/-/entities-4.5.0.tgz,
      }
    engines: { node: '>=0.12' }

  env-cmd@10.1.0:
    resolution:
      {
        integrity: sha512-mMdWTT9XKN7yNth/6N6g2GuKuJTsKMDHlQFUDacb/heQRRWOTIZ42t1rMHnQu4jYxU1ajdTeJM+9eEETlqToMA==,
        tarball: https://registry.npmjs.org/env-cmd/-/env-cmd-10.1.0.tgz,
      }
    engines: { node: '>=8.0.0' }
    hasBin: true

  env-paths@2.2.1:
    resolution:
      {
        integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==,
        tarball: https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz,
      }
    engines: { node: '>=6' }

  error-ex@1.3.2:
    resolution:
      {
        integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==,
        tarball: https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz,
      }

  error-stack-parser@2.1.4:
    resolution:
      {
        integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==,
        tarball: https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz,
      }

  es-abstract@1.24.0:
    resolution:
      {
        integrity: sha512-WSzPgsdLtTcQwm4CROfS5ju2Wa1QQcVeT37jFjYzdFz1r9ahadC8B8/a4qxJxM+09F18iumCdRmlr96ZYkQvEg==,
        tarball: https://registry.npmjs.org/es-abstract/-/es-abstract-1.24.0.tgz,
      }
    engines: { node: '>= 0.4' }

  es-define-property@1.0.1:
    resolution:
      {
        integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==,
        tarball: https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz,
      }
    engines: { node: '>= 0.4' }

  es-errors@1.3.0:
    resolution:
      {
        integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==,
        tarball: https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz,
      }
    engines: { node: '>= 0.4' }

  es-iterator-helpers@1.2.1:
    resolution:
      {
        integrity: sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==,
        tarball: https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.2.1.tgz,
      }
    engines: { node: '>= 0.4' }

  es-object-atoms@1.1.1:
    resolution:
      {
        integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==,
        tarball: https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz,
      }
    engines: { node: '>= 0.4' }

  es-set-tostringtag@2.1.0:
    resolution:
      {
        integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==,
        tarball: https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  es-shim-unscopables@1.1.0:
    resolution:
      {
        integrity: sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==,
        tarball: https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  es-to-primitive@1.3.0:
    resolution:
      {
        integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==,
        tarball: https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz,
      }
    engines: { node: '>= 0.4' }

  esbuild@0.24.2:
    resolution:
      {
        integrity: sha512-+9egpBW8I3CD5XPe0n6BfT5fxLzxrlDzqydF3aviG+9ni1lDC/OvMHcxqEFV0+LANZG5R1bFMWfUrjVsdwxJvA==,
        tarball: https://registry.npmjs.org/esbuild/-/esbuild-0.24.2.tgz,
      }
    engines: { node: '>=18' }
    hasBin: true

  escalade@3.2.0:
    resolution:
      {
        integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==,
        tarball: https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz,
      }
    engines: { node: '>=6' }

  escape-string-regexp@1.0.5:
    resolution:
      {
        integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==,
        tarball: https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz,
      }
    engines: { node: '>=0.8.0' }

  escape-string-regexp@2.0.0:
    resolution:
      {
        integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==,
        tarball: https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz,
      }
    engines: { node: '>=8' }

  escape-string-regexp@4.0.0:
    resolution:
      {
        integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==,
        tarball: https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz,
      }
    engines: { node: '>=10' }

  eslint-config-prettier@9.1.0:
    resolution:
      {
        integrity: sha512-NSWl5BFQWEPi1j4TjVNItzYV7dZXZ+wP6I6ZhrBGpChQhZRUaElihE9uRRkcbRnNb76UMKDF3r+WTmNcGPKsqw==,
        tarball: https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-9.1.0.tgz,
      }
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-prettier@5.4.1:
    resolution:
      {
        integrity: sha512-9dF+KuU/Ilkq27A8idRP7N2DH8iUR6qXcjF3FR2wETY21PZdBrIjwCau8oboyGj9b7etWmTGEeM8e7oOed6ZWg==,
        tarball: https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-5.4.1.tgz,
      }
    engines: { node: ^14.18.0 || >=16.0.0 }
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '>= 7.0.0 <10.0.0 || >=10.1.0'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true

  eslint-plugin-react@7.37.5:
    resolution:
      {
        integrity: sha512-Qteup0SqU15kdocexFNAJMvCJEfa2xUKNV4CC1xsVMrIIqEy3SQ/rqyxCWNzfrd3/ldy6HMlD2e0JDVpDg2qIA==,
        tarball: https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.37.5.tgz,
      }
    engines: { node: '>=4' }
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-plugin-simple-import-sort@12.1.1:
    resolution:
      {
        integrity: sha512-6nuzu4xwQtE3332Uz0to+TxDQYRLTKRESSc2hefVT48Zc8JthmN23Gx9lnYhu0FtkRSL1oxny3kJ2aveVhmOVA==,
        tarball: https://registry.npmjs.org/eslint-plugin-simple-import-sort/-/eslint-plugin-simple-import-sort-12.1.1.tgz,
      }
    peerDependencies:
      eslint: '>=5.0.0'

  eslint-plugin-tailwindcss@3.18.0:
    resolution:
      {
        integrity: sha512-PQDU4ZMzFH0eb2DrfHPpbgo87Zgg2EXSMOj1NSfzdZm+aJzpuwGerfowMIaVehSREEa0idbf/eoNYAOHSJoDAQ==,
        tarball: https://registry.npmjs.org/eslint-plugin-tailwindcss/-/eslint-plugin-tailwindcss-3.18.0.tgz,
      }
    engines: { node: '>=18.12.0' }
    peerDependencies:
      tailwindcss: ^3.4.0

  eslint-scope@8.4.0:
    resolution:
      {
        integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==,
        tarball: https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.4.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  eslint-visitor-keys@3.4.3:
    resolution:
      {
        integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==,
        tarball: https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  eslint-visitor-keys@4.2.1:
    resolution:
      {
        integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==,
        tarball: https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  eslint@9.16.0:
    resolution:
      {
        integrity: sha512-whp8mSQI4C8VXd+fLgSM0lh3UlmcFtVwUQjyKCFfsp+2ItAIYhlq/hqGahGqHE6cv9unM41VlqKk2VtKYR2TaA==,
        tarball: https://registry.npmjs.org/eslint/-/eslint-9.16.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.4.0:
    resolution:
      {
        integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==,
        tarball: https://registry.npmjs.org/espree/-/espree-10.4.0.tgz,
      }
    engines: { node: ^18.18.0 || ^20.9.0 || >=21.1.0 }

  esquery@1.6.0:
    resolution:
      {
        integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==,
        tarball: https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz,
      }
    engines: { node: '>=0.10' }

  esrecurse@4.3.0:
    resolution:
      {
        integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==,
        tarball: https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz,
      }
    engines: { node: '>=4.0' }

  estraverse@5.3.0:
    resolution:
      {
        integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==,
        tarball: https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz,
      }
    engines: { node: '>=4.0' }

  estree-walker@2.0.2:
    resolution:
      {
        integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==,
        tarball: https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz,
      }

  esutils@2.0.3:
    resolution:
      {
        integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==,
        tarball: https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz,
      }
    engines: { node: '>=0.10.0' }

  execa@8.0.1:
    resolution:
      {
        integrity: sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==,
        tarball: https://registry.npmjs.org/execa/-/execa-8.0.1.tgz,
      }
    engines: { node: '>=16.17' }

  expect@29.7.0:
    resolution:
      {
        integrity: sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==,
        tarball: https://registry.npmjs.org/expect/-/expect-29.7.0.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  external-editor@3.1.0:
    resolution:
      {
        integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==,
        tarball: https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz,
      }
    engines: { node: '>=4' }

  extract-files@13.0.0:
    resolution:
      {
        integrity: sha512-FXD+2Tsr8Iqtm3QZy1Zmwscca7Jx3mMC5Crr+sEP1I303Jy1CYMuYCm7hRTplFNg3XdUavErkxnTzpaqdSoi6g==,
        tarball: https://registry.npmjs.org/extract-files/-/extract-files-13.0.0.tgz,
      }
    engines: { node: ^14.17.0 || ^16.0.0 || >= 18.0.0 }

  fast-decode-uri-component@1.0.1:
    resolution:
      {
        integrity: sha512-WKgKWg5eUxvRZGwW8FvfbaH7AXSh2cL+3j5fMGzUMCxWBJ3dV3a7Wz8y2f/uQ0e3B6WmodD3oS54jTQ9HVTIIg==,
        tarball: https://registry.npmjs.org/fast-decode-uri-component/-/fast-decode-uri-component-1.0.1.tgz,
      }

  fast-deep-equal@3.1.3:
    resolution:
      {
        integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==,
        tarball: https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz,
      }

  fast-diff@1.3.0:
    resolution:
      {
        integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==,
        tarball: https://registry.npmjs.org/fast-diff/-/fast-diff-1.3.0.tgz,
      }

  fast-glob@3.3.3:
    resolution:
      {
        integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==,
        tarball: https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz,
      }
    engines: { node: '>=8.6.0' }

  fast-json-stable-stringify@2.1.0:
    resolution:
      {
        integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==,
        tarball: https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz,
      }

  fast-levenshtein@2.0.6:
    resolution:
      {
        integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==,
        tarball: https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz,
      }

  fast-querystring@1.1.2:
    resolution:
      {
        integrity: sha512-g6KuKWmFXc0fID8WWH0jit4g0AGBoJhCkJMb1RmbsSEUNvQ+ZC8D6CUZ+GtF8nMzSPXnhiePyyqqipzNNEnHjg==,
        tarball: https://registry.npmjs.org/fast-querystring/-/fast-querystring-1.1.2.tgz,
      }

  fast-shallow-equal@1.0.0:
    resolution:
      {
        integrity: sha512-HPtaa38cPgWvaCFmRNhlc6NG7pv6NUHqjPgVAkWGoB9mQMwYB27/K0CvOM5Czy+qpT3e8XJ6Q4aPAnzpNpzNaw==,
        tarball: https://registry.npmjs.org/fast-shallow-equal/-/fast-shallow-equal-1.0.0.tgz,
      }

  fast-uri@3.0.6:
    resolution:
      {
        integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==,
        tarball: https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz,
      }

  fast-url-parser@1.1.3:
    resolution:
      {
        integrity: sha512-5jOCVXADYNuRkKFzNJ0dCCewsZiYo0dz8QNYljkOpFC6r2U4OBmKtvm/Tsuh4w1YYdDqDb31a8TVhBJ2OJKdqQ==,
        tarball: https://registry.npmjs.org/fast-url-parser/-/fast-url-parser-1.1.3.tgz,
      }

  fastest-stable-stringify@2.0.2:
    resolution:
      {
        integrity: sha512-bijHueCGd0LqqNK9b5oCMHc0MluJAx0cwqASgbWMvkO01lCYgIhacVRLcaDz3QnyYIRNJRDwMb41VuT6pHJ91Q==,
        tarball: https://registry.npmjs.org/fastest-stable-stringify/-/fastest-stable-stringify-2.0.2.tgz,
      }

  fastq@1.19.1:
    resolution:
      {
        integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==,
        tarball: https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz,
      }

  fb-watchman@2.0.2:
    resolution:
      {
        integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==,
        tarball: https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz,
      }

  fbjs-css-vars@1.0.2:
    resolution:
      {
        integrity: sha512-b2XGFAFdWZWg0phtAWLHCk836A1Xann+I+Dgd3Gk64MHKZO44FfoD1KxyvbSh0qZsIoXQGGlVztIY+oitJPpRQ==,
        tarball: https://registry.npmjs.org/fbjs-css-vars/-/fbjs-css-vars-1.0.2.tgz,
      }

  fbjs@3.0.5:
    resolution:
      {
        integrity: sha512-ztsSx77JBtkuMrEypfhgc3cI0+0h+svqeie7xHbh1k/IKdcydnvadp/mUaGgjAOXQmQSxsqgaRhS3q9fy+1kxg==,
        tarball: https://registry.npmjs.org/fbjs/-/fbjs-3.0.5.tgz,
      }

  fetch-blob@3.2.0:
    resolution:
      {
        integrity: sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==,
        tarball: https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz,
      }
    engines: { node: ^12.20 || >= 14.13 }

  fflate@0.4.8:
    resolution:
      {
        integrity: sha512-FJqqoDBR00Mdj9ppamLa/Y7vxm+PRmNWA67N846RvsoYVMKB4q3y/de5PA7gUmRMYK/8CMz2GDZQmCRN1wBcWA==,
        tarball: https://registry.npmjs.org/fflate/-/fflate-0.4.8.tgz,
      }

  figures@3.2.0:
    resolution:
      {
        integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==,
        tarball: https://registry.npmjs.org/figures/-/figures-3.2.0.tgz,
      }
    engines: { node: '>=8' }

  file-entry-cache@8.0.0:
    resolution:
      {
        integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==,
        tarball: https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-8.0.0.tgz,
      }
    engines: { node: '>=16.0.0' }

  fill-range@7.1.1:
    resolution:
      {
        integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==,
        tarball: https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz,
      }
    engines: { node: '>=8' }

  find-up@4.1.0:
    resolution:
      {
        integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==,
        tarball: https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz,
      }
    engines: { node: '>=8' }

  find-up@5.0.0:
    resolution:
      {
        integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==,
        tarball: https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz,
      }
    engines: { node: '>=10' }

  find-up@7.0.0:
    resolution:
      {
        integrity: sha512-YyZM99iHrqLKjmt4LJDj58KI+fYyufRLBSYcqycxf//KpBk9FoewoGX0450m9nB44qrZnovzC2oeP5hUibxc/g==,
        tarball: https://registry.npmjs.org/find-up/-/find-up-7.0.0.tgz,
      }
    engines: { node: '>=18' }

  flat-cache@4.0.1:
    resolution:
      {
        integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==,
        tarball: https://registry.npmjs.org/flat-cache/-/flat-cache-4.0.1.tgz,
      }
    engines: { node: '>=16' }

  flat-cache@6.1.9:
    resolution:
      {
        integrity: sha512-DUqiKkTlAfhtl7g78IuwqYM+YqvT+as0mY+EVk6mfimy19U79pJCzDZQsnqk3Ou/T6hFXWLGbwbADzD/c8Tydg==,
        tarball: https://registry.npmjs.org/flat-cache/-/flat-cache-6.1.9.tgz,
      }

  flatted@3.3.3:
    resolution:
      {
        integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==,
        tarball: https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz,
      }

  follow-redirects@1.15.9:
    resolution:
      {
        integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==,
        tarball: https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz,
      }
    engines: { node: '>=4.0' }
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.5:
    resolution:
      {
        integrity: sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==,
        tarball: https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz,
      }
    engines: { node: '>= 0.4' }

  foreground-child@3.3.1:
    resolution:
      {
        integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==,
        tarball: https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz,
      }
    engines: { node: '>=14' }

  form-data@4.0.2:
    resolution:
      {
        integrity: sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==,
        tarball: https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz,
      }
    engines: { node: '>= 6' }

  formdata-polyfill@4.0.10:
    resolution:
      {
        integrity: sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==,
        tarball: https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz,
      }
    engines: { node: '>=12.20.0' }

  fraction.js@4.3.7:
    resolution:
      {
        integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==,
        tarball: https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz,
      }

  fs.realpath@1.0.0:
    resolution:
      {
        integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==,
        tarball: https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz,
      }

  fsevents@2.3.3:
    resolution:
      {
        integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==,
        tarball: https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz,
      }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]

  function-bind@1.1.2:
    resolution:
      {
        integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==,
        tarball: https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz,
      }

  function.prototype.name@1.1.8:
    resolution:
      {
        integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==,
        tarball: https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz,
      }
    engines: { node: '>= 0.4' }

  functions-have-names@1.2.3:
    resolution:
      {
        integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==,
        tarball: https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz,
      }

  gensync@1.0.0-beta.2:
    resolution:
      {
        integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==,
        tarball: https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz,
      }
    engines: { node: '>=6.9.0' }

  get-caller-file@2.0.5:
    resolution:
      {
        integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==,
        tarball: https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz,
      }
    engines: { node: 6.* || 8.* || >= 10.* }

  get-intrinsic@1.3.0:
    resolution:
      {
        integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==,
        tarball: https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz,
      }
    engines: { node: '>= 0.4' }

  get-nonce@1.0.1:
    resolution:
      {
        integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==,
        tarball: https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz,
      }
    engines: { node: '>=6' }

  get-proto@1.0.1:
    resolution:
      {
        integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==,
        tarball: https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz,
      }
    engines: { node: '>= 0.4' }

  get-stream@8.0.1:
    resolution:
      {
        integrity: sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==,
        tarball: https://registry.npmjs.org/get-stream/-/get-stream-8.0.1.tgz,
      }
    engines: { node: '>=16' }

  get-symbol-description@1.1.0:
    resolution:
      {
        integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==,
        tarball: https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  git-raw-commits@4.0.0:
    resolution:
      {
        integrity: sha512-ICsMM1Wk8xSGMowkOmPrzo2Fgmfo4bMHLNX6ytHjajRJUqvHOw/TFapQ+QG75c3X/tTDDhOSRPGC52dDbNM8FQ==,
        tarball: https://registry.npmjs.org/git-raw-commits/-/git-raw-commits-4.0.0.tgz,
      }
    engines: { node: '>=16' }
    hasBin: true

  glob-parent@5.1.2:
    resolution:
      {
        integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==,
        tarball: https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz,
      }
    engines: { node: '>= 6' }

  glob-parent@6.0.2:
    resolution:
      {
        integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==,
        tarball: https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz,
      }
    engines: { node: '>=10.13.0' }

  glob@10.4.5:
    resolution:
      {
        integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==,
        tarball: https://registry.npmjs.org/glob/-/glob-10.4.5.tgz,
      }
    hasBin: true

  glob@7.2.3:
    resolution:
      {
        integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==,
        tarball: https://registry.npmjs.org/glob/-/glob-7.2.3.tgz,
      }
    deprecated: Glob versions prior to v9 are no longer supported

  glob@9.3.5:
    resolution:
      {
        integrity: sha512-e1LleDykUz2Iu+MTYdkSsuWX8lvAjAcs0Xef0lNIu0S2wOAzuTxCJtcd9S3cijlwYF18EsU3rzb8jPVobxDh9Q==,
        tarball: https://registry.npmjs.org/glob/-/glob-9.3.5.tgz,
      }
    engines: { node: '>=16 || 14 >=14.17' }

  global-directory@4.0.1:
    resolution:
      {
        integrity: sha512-wHTUcDUoZ1H5/0iVqEudYW4/kAlN5cZ3j/bXn0Dpbizl9iaUVeWSHqiOjsgk6OW2bkLclbBjzewBz6weQ1zA2Q==,
        tarball: https://registry.npmjs.org/global-directory/-/global-directory-4.0.1.tgz,
      }
    engines: { node: '>=18' }

  globals@11.12.0:
    resolution:
      {
        integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==,
        tarball: https://registry.npmjs.org/globals/-/globals-11.12.0.tgz,
      }
    engines: { node: '>=4' }

  globals@14.0.0:
    resolution:
      {
        integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==,
        tarball: https://registry.npmjs.org/globals/-/globals-14.0.0.tgz,
      }
    engines: { node: '>=18' }

  globals@15.15.0:
    resolution:
      {
        integrity: sha512-7ACyT3wmyp3I61S4fG682L0VA2RGD9otkqGJIwNUMF1SWUombIIk+af1unuDYgMm082aHYwD+mzJvv9Iu8dsgg==,
        tarball: https://registry.npmjs.org/globals/-/globals-15.15.0.tgz,
      }
    engines: { node: '>=18' }

  globalthis@1.0.4:
    resolution:
      {
        integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==,
        tarball: https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz,
      }
    engines: { node: '>= 0.4' }

  globby@11.1.0:
    resolution:
      {
        integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==,
        tarball: https://registry.npmjs.org/globby/-/globby-11.1.0.tgz,
      }
    engines: { node: '>=10' }

  globrex@0.1.2:
    resolution:
      {
        integrity: sha512-uHJgbwAMwNFf5mLst7IWLNg14x1CkeqglJb/K3doi4dw6q2IvAAmM/Y81kevy83wP+Sst+nutFTYOGg3d1lsxg==,
        tarball: https://registry.npmjs.org/globrex/-/globrex-0.1.2.tgz,
      }

  gopd@1.2.0:
    resolution:
      {
        integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==,
        tarball: https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz,
      }
    engines: { node: '>= 0.4' }

  graceful-fs@4.2.11:
    resolution:
      {
        integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==,
        tarball: https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz,
      }

  graphemer@1.4.0:
    resolution:
      {
        integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==,
        tarball: https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz,
      }

  graphql-config@5.1.5:
    resolution:
      {
        integrity: sha512-mG2LL1HccpU8qg5ajLROgdsBzx/o2M6kgI3uAmoaXiSH9PCUbtIyLomLqUtCFaAeG2YCFsl0M5cfQ9rKmDoMVA==,
        tarball: https://registry.npmjs.org/graphql-config/-/graphql-config-5.1.5.tgz,
      }
    engines: { node: '>= 16.0.0' }
    peerDependencies:
      cosmiconfig-toml-loader: ^1.0.0
      graphql: ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0
    peerDependenciesMeta:
      cosmiconfig-toml-loader:
        optional: true

  graphql-request@6.1.0:
    resolution:
      {
        integrity: sha512-p+XPfS4q7aIpKVcgmnZKhMNqhltk20hfXtkaIkTfjjmiKMJ5xrt5c743cL03y/K7y1rg3WrIC49xGiEQ4mxdNw==,
        tarball: https://registry.npmjs.org/graphql-request/-/graphql-request-6.1.0.tgz,
      }
    peerDependencies:
      graphql: 14 - 16

  graphql-tag@2.12.6:
    resolution:
      {
        integrity: sha512-FdSNcu2QQcWnM2VNvSCCDCVS5PpPqpzgFT8+GXzqJuoDd0CBncxCY278u4mhRO7tMgo2JjgJA5aZ+nWSQ/Z+xg==,
        tarball: https://registry.npmjs.org/graphql-tag/-/graphql-tag-2.12.6.tgz,
      }
    engines: { node: '>=10' }
    peerDependencies:
      graphql: ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0

  graphql-ws@6.0.5:
    resolution:
      {
        integrity: sha512-HzYw057ch0hx2gZjkbgk1pur4kAtgljlWRP+Gccudqm3BRrTpExjWCQ9OHdIsq47Y6lHL++1lTvuQHhgRRcevw==,
        tarball: https://registry.npmjs.org/graphql-ws/-/graphql-ws-6.0.5.tgz,
      }
    engines: { node: '>=20' }
    peerDependencies:
      '@fastify/websocket': ^10 || ^11
      crossws: ~0.3
      graphql: ^15.10.1 || ^16
      uWebSockets.js: ^20
      ws: ^8
    peerDependenciesMeta:
      '@fastify/websocket':
        optional: true
      crossws:
        optional: true
      uWebSockets.js:
        optional: true
      ws:
        optional: true

  graphql@16.8.1:
    resolution:
      {
        integrity: sha512-59LZHPdGZVh695Ud9lRzPBVTtlX9ZCV150Er2W43ro37wVof0ctenSaskPPjN7lVTIN8mSZt8PHUNKZuNQUuxw==,
        tarball: https://registry.npmjs.org/graphql/-/graphql-16.8.1.tgz,
      }
    engines: { node: ^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0 }

  has-bigints@1.1.0:
    resolution:
      {
        integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==,
        tarball: https://registry.npmjs.org/has-bigints/-/has-bigints-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  has-flag@4.0.0:
    resolution:
      {
        integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==,
        tarball: https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz,
      }
    engines: { node: '>=8' }

  has-property-descriptors@1.0.2:
    resolution:
      {
        integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==,
        tarball: https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz,
      }

  has-proto@1.2.0:
    resolution:
      {
        integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==,
        tarball: https://registry.npmjs.org/has-proto/-/has-proto-1.2.0.tgz,
      }
    engines: { node: '>= 0.4' }

  has-symbols@1.1.0:
    resolution:
      {
        integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==,
        tarball: https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  has-tostringtag@1.0.2:
    resolution:
      {
        integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==,
        tarball: https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  hasown@2.0.2:
    resolution:
      {
        integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==,
        tarball: https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  header-case@2.0.4:
    resolution:
      {
        integrity: sha512-H/vuk5TEEVZwrR0lp2zed9OCo1uAILMlx0JEMgC26rzyJJ3N1v6XkwHHXJQdR2doSjcGPM6OKPYoJgf0plJ11Q==,
        tarball: https://registry.npmjs.org/header-case/-/header-case-2.0.4.tgz,
      }

  hoist-non-react-statics@3.3.2:
    resolution:
      {
        integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==,
        tarball: https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz,
      }

  hookified@1.9.1:
    resolution:
      {
        integrity: sha512-u3pxtGhKjcSXnGm1CX6aXS9xew535j3lkOCegbA6jdyh0BaAjTbXI4aslKstCr6zUNtoCxFGFKwjbSHdGrMB8g==,
        tarball: https://registry.npmjs.org/hookified/-/hookified-1.9.1.tgz,
      }

  html-parse-stringify@3.0.1:
    resolution:
      {
        integrity: sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg==,
        tarball: https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz,
      }

  http-proxy-agent@7.0.2:
    resolution:
      {
        integrity: sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==,
        tarball: https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz,
      }
    engines: { node: '>= 14' }

  https-proxy-agent@5.0.1:
    resolution:
      {
        integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==,
        tarball: https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz,
      }
    engines: { node: '>= 6' }

  https-proxy-agent@7.0.6:
    resolution:
      {
        integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==,
        tarball: https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz,
      }
    engines: { node: '>= 14' }

  human-signals@5.0.0:
    resolution:
      {
        integrity: sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==,
        tarball: https://registry.npmjs.org/human-signals/-/human-signals-5.0.0.tgz,
      }
    engines: { node: '>=16.17.0' }

  hyphenate-style-name@1.1.0:
    resolution:
      {
        integrity: sha512-WDC/ui2VVRrz3jOVi+XtjqkDjiVjTtFaAGiW37k6b+ohyQ5wYDOGkvCZa8+H0nx3gyvv0+BST9xuOgIyGQ00gw==,
        tarball: https://registry.npmjs.org/hyphenate-style-name/-/hyphenate-style-name-1.1.0.tgz,
      }

  i18next-browser-languagedetector@7.2.0:
    resolution:
      {
        integrity: sha512-U00DbDtFIYD3wkWsr2aVGfXGAj2TgnELzOX9qv8bT0aJtvPV9CRO77h+vgmHFBMe7LAxdwvT/7VkCWGya6L3tA==,
        tarball: https://registry.npmjs.org/i18next-browser-languagedetector/-/i18next-browser-languagedetector-7.2.0.tgz,
      }

  i18next-locize-backend@6.4.1:
    resolution:
      {
        integrity: sha512-D8XxklzzsftydwEKxEa5y9Hz5TMxY5Z68ciwMyIhB/g/vAGW4x8ZW5u27guCppWH+pAzdIYkT8E47lfjcBak6w==,
        tarball: https://registry.npmjs.org/i18next-locize-backend/-/i18next-locize-backend-6.4.1.tgz,
      }

  i18next@23.10.1:
    resolution:
      {
        integrity: sha512-NDiIzFbcs3O9PXpfhkjyf7WdqFn5Vq6mhzhtkXzj51aOcNuPNcTwuYNuXCpHsanZGHlHKL35G7huoFeVic1hng==,
        tarball: https://registry.npmjs.org/i18next/-/i18next-23.10.1.tgz,
      }

  i@0.3.7:
    resolution:
      {
        integrity: sha512-FYz4wlXgkQwIPqhzC5TdNMLSE5+GS1IIDJZY/1ZiEPCT2S3COUVZeT5OW4BmW4r5LHLQuOosSwsvnroG9GR59Q==,
        tarball: https://registry.npmjs.org/i/-/i-0.3.7.tgz,
      }
    engines: { node: '>=0.4' }

  iconv-lite@0.4.24:
    resolution:
      {
        integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==,
        tarball: https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz,
      }
    engines: { node: '>=0.10.0' }

  ieee754@1.2.1:
    resolution:
      {
        integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==,
        tarball: https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz,
      }

  ignore@5.3.2:
    resolution:
      {
        integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==,
        tarball: https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz,
      }
    engines: { node: '>= 4' }

  ignore@7.0.5:
    resolution:
      {
        integrity: sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==,
        tarball: https://registry.npmjs.org/ignore/-/ignore-7.0.5.tgz,
      }
    engines: { node: '>= 4' }

  immutable@3.7.6:
    resolution:
      {
        integrity: sha512-AizQPcaofEtO11RZhPPHBOJRdo/20MKQF9mBLnVkBoyHi1/zXK8fzVdnEpSV9gxqtnh6Qomfp3F0xT5qP/vThw==,
        tarball: https://registry.npmjs.org/immutable/-/immutable-3.7.6.tgz,
      }
    engines: { node: '>=0.8.0' }

  immutable@4.3.7:
    resolution:
      {
        integrity: sha512-1hqclzwYwjRDFLjcFxOM5AYkkG0rpFPpr1RLPMEuGczoS7YA8gLhy8SWXYRAA/XwfEHpfo3cw5JGioS32fnMRw==,
        tarball: https://registry.npmjs.org/immutable/-/immutable-4.3.7.tgz,
      }

  import-fresh@3.3.1:
    resolution:
      {
        integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==,
        tarball: https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz,
      }
    engines: { node: '>=6' }

  import-from@4.0.0:
    resolution:
      {
        integrity: sha512-P9J71vT5nLlDeV8FHs5nNxaLbrpfAV5cF5srvbZfpwpcJoM/xZR3hiv+q+SAnuSmuGbXMWud063iIMx/V/EWZQ==,
        tarball: https://registry.npmjs.org/import-from/-/import-from-4.0.0.tgz,
      }
    engines: { node: '>=12.2' }

  import-meta-resolve@4.1.0:
    resolution:
      {
        integrity: sha512-I6fiaX09Xivtk+THaMfAwnA3MVA5Big1WHF1Dfx9hFuvNIWpXnorlkzhcQf6ehrqQiiZECRt1poOAkPmer3ruw==,
        tarball: https://registry.npmjs.org/import-meta-resolve/-/import-meta-resolve-4.1.0.tgz,
      }

  imurmurhash@0.1.4:
    resolution:
      {
        integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==,
        tarball: https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz,
      }
    engines: { node: '>=0.8.19' }

  indent-string@4.0.0:
    resolution:
      {
        integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==,
        tarball: https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz,
      }
    engines: { node: '>=8' }

  inflight@1.0.6:
    resolution:
      {
        integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==,
        tarball: https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz,
      }
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution:
      {
        integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==,
        tarball: https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz,
      }

  ini@4.1.1:
    resolution:
      {
        integrity: sha512-QQnnxNyfvmHFIsj7gkPcYymR8Jdw/o7mp5ZFihxn6h8Ci6fh3Dx4E1gPjpQEpIuPo9XVNY/ZUwh4BPMjGyL01g==,
        tarball: https://registry.npmjs.org/ini/-/ini-4.1.1.tgz,
      }
    engines: { node: ^14.17.0 || ^16.13.0 || >=18.0.0 }

  inline-style-prefixer@7.0.1:
    resolution:
      {
        integrity: sha512-lhYo5qNTQp3EvSSp3sRvXMbVQTLrvGV6DycRMJ5dm2BLMiJ30wpXKdDdgX+GmJZ5uQMucwRKHamXSst3Sj/Giw==,
        tarball: https://registry.npmjs.org/inline-style-prefixer/-/inline-style-prefixer-7.0.1.tgz,
      }

  inquirer@8.2.6:
    resolution:
      {
        integrity: sha512-M1WuAmb7pn9zdFRtQYk26ZBoY043Sse0wVDdk4Bppr+JOXyQYybdtvK+l9wUibhtjdjvtoiNy8tk+EgsYIUqKg==,
        tarball: https://registry.npmjs.org/inquirer/-/inquirer-8.2.6.tgz,
      }
    engines: { node: '>=12.0.0' }

  internal-slot@1.1.0:
    resolution:
      {
        integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==,
        tarball: https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  invariant@2.2.4:
    resolution:
      {
        integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==,
        tarball: https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz,
      }

  is-absolute@1.0.0:
    resolution:
      {
        integrity: sha512-dOWoqflvcydARa360Gvv18DZ/gRuHKi2NU/wU5X1ZFzdYfH29nkiNZsF3mp4OJ3H4yo9Mx8A/uAGNzpzPN3yBA==,
        tarball: https://registry.npmjs.org/is-absolute/-/is-absolute-1.0.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  is-array-buffer@3.0.5:
    resolution:
      {
        integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==,
        tarball: https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz,
      }
    engines: { node: '>= 0.4' }

  is-arrayish@0.2.1:
    resolution:
      {
        integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==,
        tarball: https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz,
      }

  is-async-function@2.1.1:
    resolution:
      {
        integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==,
        tarball: https://registry.npmjs.org/is-async-function/-/is-async-function-2.1.1.tgz,
      }
    engines: { node: '>= 0.4' }

  is-bigint@1.1.0:
    resolution:
      {
        integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==,
        tarball: https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  is-binary-path@2.1.0:
    resolution:
      {
        integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==,
        tarball: https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz,
      }
    engines: { node: '>=8' }

  is-boolean-object@1.2.2:
    resolution:
      {
        integrity: sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==,
        tarball: https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.2.tgz,
      }
    engines: { node: '>= 0.4' }

  is-callable@1.2.7:
    resolution:
      {
        integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==,
        tarball: https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz,
      }
    engines: { node: '>= 0.4' }

  is-core-module@2.16.1:
    resolution:
      {
        integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==,
        tarball: https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz,
      }
    engines: { node: '>= 0.4' }

  is-data-view@1.0.2:
    resolution:
      {
        integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==,
        tarball: https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  is-date-object@1.1.0:
    resolution:
      {
        integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==,
        tarball: https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  is-extglob@2.1.1:
    resolution:
      {
        integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==,
        tarball: https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz,
      }
    engines: { node: '>=0.10.0' }

  is-finalizationregistry@1.1.1:
    resolution:
      {
        integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==,
        tarball: https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz,
      }
    engines: { node: '>= 0.4' }

  is-fullwidth-code-point@3.0.0:
    resolution:
      {
        integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==,
        tarball: https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz,
      }
    engines: { node: '>=8' }

  is-generator-function@1.1.0:
    resolution:
      {
        integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==,
        tarball: https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  is-glob@4.0.3:
    resolution:
      {
        integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==,
        tarball: https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz,
      }
    engines: { node: '>=0.10.0' }

  is-interactive@1.0.0:
    resolution:
      {
        integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==,
        tarball: https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz,
      }
    engines: { node: '>=8' }

  is-lower-case@2.0.2:
    resolution:
      {
        integrity: sha512-bVcMJy4X5Og6VZfdOZstSexlEy20Sr0k/p/b2IlQJlfdKAQuMpiv5w2Ccxb8sKdRUNAG1PnHVHjFSdRDVS6NlQ==,
        tarball: https://registry.npmjs.org/is-lower-case/-/is-lower-case-2.0.2.tgz,
      }

  is-map@2.0.3:
    resolution:
      {
        integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==,
        tarball: https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz,
      }
    engines: { node: '>= 0.4' }

  is-negative-zero@2.0.3:
    resolution:
      {
        integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==,
        tarball: https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.3.tgz,
      }
    engines: { node: '>= 0.4' }

  is-number-object@1.1.1:
    resolution:
      {
        integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==,
        tarball: https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz,
      }
    engines: { node: '>= 0.4' }

  is-number@7.0.0:
    resolution:
      {
        integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==,
        tarball: https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz,
      }
    engines: { node: '>=0.12.0' }

  is-obj@2.0.0:
    resolution:
      {
        integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==,
        tarball: https://registry.npmjs.org/is-obj/-/is-obj-2.0.0.tgz,
      }
    engines: { node: '>=8' }

  is-plain-obj@4.1.0:
    resolution:
      {
        integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==,
        tarball: https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz,
      }
    engines: { node: '>=12' }

  is-regex@1.2.1:
    resolution:
      {
        integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==,
        tarball: https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz,
      }
    engines: { node: '>= 0.4' }

  is-relative@1.0.0:
    resolution:
      {
        integrity: sha512-Kw/ReK0iqwKeu0MITLFuj0jbPAmEiOsIwyIXvvbfa6QfmN9pkD1M+8pdk7Rl/dTKbH34/XBFMbgD4iMJhLQbGA==,
        tarball: https://registry.npmjs.org/is-relative/-/is-relative-1.0.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  is-set@2.0.3:
    resolution:
      {
        integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==,
        tarball: https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz,
      }
    engines: { node: '>= 0.4' }

  is-shared-array-buffer@1.0.4:
    resolution:
      {
        integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==,
        tarball: https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz,
      }
    engines: { node: '>= 0.4' }

  is-stream@3.0.0:
    resolution:
      {
        integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==,
        tarball: https://registry.npmjs.org/is-stream/-/is-stream-3.0.0.tgz,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  is-string@1.1.1:
    resolution:
      {
        integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==,
        tarball: https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz,
      }
    engines: { node: '>= 0.4' }

  is-symbol@1.1.1:
    resolution:
      {
        integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==,
        tarball: https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz,
      }
    engines: { node: '>= 0.4' }

  is-text-path@2.0.0:
    resolution:
      {
        integrity: sha512-+oDTluR6WEjdXEJMnC2z6A4FRwFoYuvShVVEGsS7ewc0UTi2QtAKMDJuL4BDEVt+5T7MjFo12RP8ghOM75oKJw==,
        tarball: https://registry.npmjs.org/is-text-path/-/is-text-path-2.0.0.tgz,
      }
    engines: { node: '>=8' }

  is-typed-array@1.1.15:
    resolution:
      {
        integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==,
        tarball: https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz,
      }
    engines: { node: '>= 0.4' }

  is-unc-path@1.0.0:
    resolution:
      {
        integrity: sha512-mrGpVd0fs7WWLfVsStvgF6iEJnbjDFZh9/emhRDcGWTduTfNHd9CHeUwH3gYIjdbwo4On6hunkztwOaAw0yllQ==,
        tarball: https://registry.npmjs.org/is-unc-path/-/is-unc-path-1.0.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  is-unicode-supported@0.1.0:
    resolution:
      {
        integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==,
        tarball: https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz,
      }
    engines: { node: '>=10' }

  is-upper-case@2.0.2:
    resolution:
      {
        integrity: sha512-44pxmxAvnnAOwBg4tHPnkfvgjPwbc5QIsSstNU+YcJ1ovxVzCWpSGosPJOZh/a1tdl81fbgnLc9LLv+x2ywbPQ==,
        tarball: https://registry.npmjs.org/is-upper-case/-/is-upper-case-2.0.2.tgz,
      }

  is-weakmap@2.0.2:
    resolution:
      {
        integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==,
        tarball: https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  is-weakref@1.1.1:
    resolution:
      {
        integrity: sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==,
        tarball: https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz,
      }
    engines: { node: '>= 0.4' }

  is-weakset@2.0.4:
    resolution:
      {
        integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==,
        tarball: https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz,
      }
    engines: { node: '>= 0.4' }

  is-windows@1.0.2:
    resolution:
      {
        integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==,
        tarball: https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz,
      }
    engines: { node: '>=0.10.0' }

  isarray@2.0.5:
    resolution:
      {
        integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==,
        tarball: https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz,
      }

  isexe@2.0.0:
    resolution:
      {
        integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==,
        tarball: https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz,
      }

  isomorphic-ws@5.0.0:
    resolution:
      {
        integrity: sha512-muId7Zzn9ywDsyXgTIafTry2sV3nySZeUDe6YedVd1Hvuuep5AsIlqK+XefWpYTyJG5e503F2xIuT2lcU6rCSw==,
        tarball: https://registry.npmjs.org/isomorphic-ws/-/isomorphic-ws-5.0.0.tgz,
      }
    peerDependencies:
      ws: '*'

  iterator.prototype@1.1.5:
    resolution:
      {
        integrity: sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==,
        tarball: https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.5.tgz,
      }
    engines: { node: '>= 0.4' }

  jackspeak@3.4.3:
    resolution:
      {
        integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==,
        tarball: https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz,
      }

  jest-diff@29.7.0:
    resolution:
      {
        integrity: sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==,
        tarball: https://registry.npmjs.org/jest-diff/-/jest-diff-29.7.0.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-get-type@29.6.3:
    resolution:
      {
        integrity: sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==,
        tarball: https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-matcher-utils@29.7.0:
    resolution:
      {
        integrity: sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==,
        tarball: https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-message-util@29.7.0:
    resolution:
      {
        integrity: sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==,
        tarball: https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-util@29.7.0:
    resolution:
      {
        integrity: sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==,
        tarball: https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jiti@1.21.7:
    resolution:
      {
        integrity: sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==,
        tarball: https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz,
      }
    hasBin: true

  jiti@2.4.2:
    resolution:
      {
        integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==,
        tarball: https://registry.npmjs.org/jiti/-/jiti-2.4.2.tgz,
      }
    hasBin: true

  jose@5.10.0:
    resolution:
      {
        integrity: sha512-s+3Al/p9g32Iq+oqXxkW//7jk2Vig6FF1CFqzVXoTUXt2qz89YWbL+OwS17NFYEvxC35n0FKeGO2LGYSxeM2Gg==,
        tarball: https://registry.npmjs.org/jose/-/jose-5.10.0.tgz,
      }

  js-cookie@2.2.1:
    resolution:
      {
        integrity: sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==,
        tarball: https://registry.npmjs.org/js-cookie/-/js-cookie-2.2.1.tgz,
      }

  js-tokens@4.0.0:
    resolution:
      {
        integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==,
        tarball: https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz,
      }

  js-yaml@4.1.0:
    resolution:
      {
        integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==,
        tarball: https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz,
      }
    hasBin: true

  jsesc@3.1.0:
    resolution:
      {
        integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==,
        tarball: https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz,
      }
    engines: { node: '>=6' }
    hasBin: true

  json-buffer@3.0.1:
    resolution:
      {
        integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==,
        tarball: https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz,
      }

  json-parse-even-better-errors@2.3.1:
    resolution:
      {
        integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==,
        tarball: https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz,
      }

  json-schema-traverse@0.4.1:
    resolution:
      {
        integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==,
        tarball: https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz,
      }

  json-schema-traverse@1.0.0:
    resolution:
      {
        integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==,
        tarball: https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz,
      }

  json-stable-stringify-without-jsonify@1.0.1:
    resolution:
      {
        integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==,
        tarball: https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz,
      }

  json-to-pretty-yaml@1.2.2:
    resolution:
      {
        integrity: sha512-rvm6hunfCcqegwYaG5T4yKJWxc9FXFgBVrcTZ4XfSVRwa5HA/Xs+vB/Eo9treYYHCeNM0nrSUr82V/M31Urc7A==,
        tarball: https://registry.npmjs.org/json-to-pretty-yaml/-/json-to-pretty-yaml-1.2.2.tgz,
      }
    engines: { node: '>= 0.2.0' }

  json5@2.2.3:
    resolution:
      {
        integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==,
        tarball: https://registry.npmjs.org/json5/-/json5-2.2.3.tgz,
      }
    engines: { node: '>=6' }
    hasBin: true

  jsonparse@1.3.1:
    resolution:
      {
        integrity: sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==,
        tarball: https://registry.npmjs.org/jsonparse/-/jsonparse-1.3.1.tgz,
      }
    engines: { '0': node >= 0.2.0 }

  jsx-ast-utils@3.3.5:
    resolution:
      {
        integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==,
        tarball: https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz,
      }
    engines: { node: '>=4.0' }

  keyv@4.5.4:
    resolution:
      {
        integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==,
        tarball: https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz,
      }

  keyv@5.3.3:
    resolution:
      {
        integrity: sha512-Rwu4+nXI9fqcxiEHtbkvoes2X+QfkTRo1TMkPfwzipGsJlJO/z69vqB4FNl9xJ3xCpAcbkvmEabZfPzrwN3+gQ==,
        tarball: https://registry.npmjs.org/keyv/-/keyv-5.3.3.tgz,
      }

  levn@0.4.1:
    resolution:
      {
        integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==,
        tarball: https://registry.npmjs.org/levn/-/levn-0.4.1.tgz,
      }
    engines: { node: '>= 0.8.0' }

  lilconfig@3.1.3:
    resolution:
      {
        integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==,
        tarball: https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz,
      }
    engines: { node: '>=14' }

  lines-and-columns@1.2.4:
    resolution:
      {
        integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==,
        tarball: https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz,
      }

  listr2@4.0.5:
    resolution:
      {
        integrity: sha512-juGHV1doQdpNT3GSTs9IUN43QJb7KHdF9uqg7Vufs/tG9VTzpFphqF4pm/ICdAABGQxsyNn9CiYA3StkI6jpwA==,
        tarball: https://registry.npmjs.org/listr2/-/listr2-4.0.5.tgz,
      }
    engines: { node: '>=12' }
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    peerDependenciesMeta:
      enquirer:
        optional: true

  locate-path@5.0.0:
    resolution:
      {
        integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==,
        tarball: https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz,
      }
    engines: { node: '>=8' }

  locate-path@6.0.0:
    resolution:
      {
        integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==,
        tarball: https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz,
      }
    engines: { node: '>=10' }

  locate-path@7.2.0:
    resolution:
      {
        integrity: sha512-gvVijfZvn7R+2qyPX8mAuKcFGDf6Nc61GdvGafQsHL0sBIxfKzA+usWn4GFC/bk+QdwPUD4kWFJLhElipq+0VA==,
        tarball: https://registry.npmjs.org/locate-path/-/locate-path-7.2.0.tgz,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  lodash.camelcase@4.3.0:
    resolution:
      {
        integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==,
        tarball: https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz,
      }

  lodash.isplainobject@4.0.6:
    resolution:
      {
        integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==,
        tarball: https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz,
      }

  lodash.kebabcase@4.1.1:
    resolution:
      {
        integrity: sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==,
        tarball: https://registry.npmjs.org/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz,
      }

  lodash.merge@4.6.2:
    resolution:
      {
        integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==,
        tarball: https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz,
      }

  lodash.mergewith@4.6.2:
    resolution:
      {
        integrity: sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ==,
        tarball: https://registry.npmjs.org/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz,
      }

  lodash.snakecase@4.1.1:
    resolution:
      {
        integrity: sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw==,
        tarball: https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz,
      }

  lodash.sortby@4.7.0:
    resolution:
      {
        integrity: sha512-HDWXG8isMntAyRF5vZ7xKuEvOhT4AhlRt/3czTSjvGUxjYCBVRQY48ViDHyfYz9VIoBkW4TMGQNapx+l3RUwdA==,
        tarball: https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz,
      }

  lodash.startcase@4.4.0:
    resolution:
      {
        integrity: sha512-+WKqsK294HMSc2jEbNgpHpd0JfIBhp7rEV4aqXWqFr6AlXov+SlcgB1Fv01y2kGe3Gc8nMW7VA0SrGuSkRfIEg==,
        tarball: https://registry.npmjs.org/lodash.startcase/-/lodash.startcase-4.4.0.tgz,
      }

  lodash.uniq@4.5.0:
    resolution:
      {
        integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==,
        tarball: https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz,
      }

  lodash.upperfirst@4.3.1:
    resolution:
      {
        integrity: sha512-sReKOYJIJf74dhJONhU4e0/shzi1trVbSWDOhKYE5XV2O+H7Sb2Dihwuc7xWxVl+DgFPyTqIN3zMfT9cq5iWDg==,
        tarball: https://registry.npmjs.org/lodash.upperfirst/-/lodash.upperfirst-4.3.1.tgz,
      }

  lodash@4.17.21:
    resolution:
      {
        integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==,
        tarball: https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz,
      }

  log-symbols@4.1.0:
    resolution:
      {
        integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==,
        tarball: https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz,
      }
    engines: { node: '>=10' }

  log-update@4.0.0:
    resolution:
      {
        integrity: sha512-9fkkDevMefjg0mmzWFBW8YkFP91OrizzkW3diF7CpG+S2EYdy4+TVfGwz1zeF8x7hCx1ovSPTOE9Ngib74qqUg==,
        tarball: https://registry.npmjs.org/log-update/-/log-update-4.0.0.tgz,
      }
    engines: { node: '>=10' }

  loose-envify@1.4.0:
    resolution:
      {
        integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==,
        tarball: https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz,
      }
    hasBin: true

  lower-case-first@2.0.2:
    resolution:
      {
        integrity: sha512-EVm/rR94FJTZi3zefZ82fLWab+GX14LJN4HrWBcuo6Evmsl9hEfnqxgcHCKb9q+mNf6EVdsjx/qucYFIIB84pg==,
        tarball: https://registry.npmjs.org/lower-case-first/-/lower-case-first-2.0.2.tgz,
      }

  lower-case@2.0.2:
    resolution:
      {
        integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==,
        tarball: https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz,
      }

  lru-cache@10.4.3:
    resolution:
      {
        integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==,
        tarball: https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz,
      }

  lru-cache@5.1.1:
    resolution:
      {
        integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==,
        tarball: https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz,
      }

  lucide-react@0.474.0:
    resolution:
      {
        integrity: sha512-CmghgHkh0OJNmxGKWc0qfPJCYHASPMVSyGY8fj3xgk4v84ItqDg64JNKFZn5hC6E0vHi6gxnbCgwhyVB09wQtA==,
        tarball: https://registry.npmjs.org/lucide-react/-/lucide-react-0.474.0.tgz,
      }
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0

  magic-string@0.30.8:
    resolution:
      {
        integrity: sha512-ISQTe55T2ao7XtlAStud6qwYPZjE4GK1S/BeVPus4jrq6JuOnQ00YKQC581RWhR122W7msZV263KzVeLoqidyQ==,
        tarball: https://registry.npmjs.org/magic-string/-/magic-string-0.30.8.tgz,
      }
    engines: { node: '>=12' }

  map-cache@0.2.2:
    resolution:
      {
        integrity: sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==,
        tarball: https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz,
      }
    engines: { node: '>=0.10.0' }

  math-intrinsics@1.1.0:
    resolution:
      {
        integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==,
        tarball: https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  mdn-data@2.0.14:
    resolution:
      {
        integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==,
        tarball: https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz,
      }

  meow@12.1.1:
    resolution:
      {
        integrity: sha512-BhXM0Au22RwUneMPwSCnyhTOizdWoIEPU9sp0Aqa1PnDMR5Wv2FGXYDjuzJEIX+Eo2Rb8xuYe5jrnm5QowQFkw==,
        tarball: https://registry.npmjs.org/meow/-/meow-12.1.1.tgz,
      }
    engines: { node: '>=16.10' }

  merge-stream@2.0.0:
    resolution:
      {
        integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==,
        tarball: https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz,
      }

  merge2@1.4.1:
    resolution:
      {
        integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==,
        tarball: https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz,
      }
    engines: { node: '>= 8' }

  meros@1.3.0:
    resolution:
      {
        integrity: sha512-2BNGOimxEz5hmjUG2FwoxCt5HN7BXdaWyFqEwxPTrJzVdABtrL4TiHTcsWSFAxPQ/tOnEaQEJh3qWq71QRMY+w==,
        tarball: https://registry.npmjs.org/meros/-/meros-1.3.0.tgz,
      }
    engines: { node: '>=13' }
    peerDependencies:
      '@types/node': '>=13'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  micromatch@4.0.8:
    resolution:
      {
        integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==,
        tarball: https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz,
      }
    engines: { node: '>=8.6' }

  mime-db@1.52.0:
    resolution:
      {
        integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==,
        tarball: https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz,
      }
    engines: { node: '>= 0.6' }

  mime-types@2.1.35:
    resolution:
      {
        integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==,
        tarball: https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz,
      }
    engines: { node: '>= 0.6' }

  mimic-fn@2.1.0:
    resolution:
      {
        integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==,
        tarball: https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz,
      }
    engines: { node: '>=6' }

  mimic-fn@4.0.0:
    resolution:
      {
        integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==,
        tarball: https://registry.npmjs.org/mimic-fn/-/mimic-fn-4.0.0.tgz,
      }
    engines: { node: '>=12' }

  minimatch@3.1.2:
    resolution:
      {
        integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==,
        tarball: https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz,
      }

  minimatch@8.0.4:
    resolution:
      {
        integrity: sha512-W0Wvr9HyFXZRGIDgCicunpQ299OKXs9RgZfaukz4qAW/pJhcpUfupc9c+OObPOFueNy8VSrZgEmDtk6Kh4WzDA==,
        tarball: https://registry.npmjs.org/minimatch/-/minimatch-8.0.4.tgz,
      }
    engines: { node: '>=16 || 14 >=14.17' }

  minimatch@9.0.5:
    resolution:
      {
        integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==,
        tarball: https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz,
      }
    engines: { node: '>=16 || 14 >=14.17' }

  minimist@1.2.8:
    resolution:
      {
        integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==,
        tarball: https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz,
      }

  minipass@4.2.8:
    resolution:
      {
        integrity: sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==,
        tarball: https://registry.npmjs.org/minipass/-/minipass-4.2.8.tgz,
      }
    engines: { node: '>=8' }

  minipass@7.1.2:
    resolution:
      {
        integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==,
        tarball: https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz,
      }
    engines: { node: '>=16 || 14 >=14.17' }

  ms@2.1.3:
    resolution:
      {
        integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==,
        tarball: https://registry.npmjs.org/ms/-/ms-2.1.3.tgz,
      }

  mute-stream@0.0.8:
    resolution:
      {
        integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==,
        tarball: https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz,
      }

  mz@2.7.0:
    resolution:
      {
        integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==,
        tarball: https://registry.npmjs.org/mz/-/mz-2.7.0.tgz,
      }

  nano-css@5.6.2:
    resolution:
      {
        integrity: sha512-+6bHaC8dSDGALM1HJjOHVXpuastdu2xFoZlC77Jh4cg+33Zcgm+Gxd+1xsnpZK14eyHObSp82+ll5y3SX75liw==,
        tarball: https://registry.npmjs.org/nano-css/-/nano-css-5.6.2.tgz,
      }
    peerDependencies:
      react: '*'
      react-dom: '*'

  nanoid@3.3.11:
    resolution:
      {
        integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==,
        tarball: https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz,
      }
    engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
    hasBin: true

  natural-compare@1.4.0:
    resolution:
      {
        integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==,
        tarball: https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz,
      }

  no-case@3.0.4:
    resolution:
      {
        integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==,
        tarball: https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz,
      }

  node-domexception@1.0.0:
    resolution:
      {
        integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==,
        tarball: https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz,
      }
    engines: { node: '>=10.5.0' }
    deprecated: Use your platform's native DOMException instead

  node-fetch@2.7.0:
    resolution:
      {
        integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==,
        tarball: https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz,
      }
    engines: { node: 4.x || >=6.0.0 }
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-fetch@3.3.2:
    resolution:
      {
        integrity: sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==,
        tarball: https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  node-int64@0.4.0:
    resolution:
      {
        integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==,
        tarball: https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz,
      }

  node-releases@2.0.19:
    resolution:
      {
        integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==,
        tarball: https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz,
      }

  normalize-path@2.1.1:
    resolution:
      {
        integrity: sha512-3pKJwH184Xo/lnH6oyP1q2pMd7HcypqqmRs91/6/i2CGtWwIKGCkOOMTm/zXbgTEWHw1uNpNi/igc3ePOYHb6w==,
        tarball: https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz,
      }
    engines: { node: '>=0.10.0' }

  normalize-path@3.0.0:
    resolution:
      {
        integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==,
        tarball: https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  normalize-range@0.1.2:
    resolution:
      {
        integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==,
        tarball: https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz,
      }
    engines: { node: '>=0.10.0' }

  npm-run-path@5.3.0:
    resolution:
      {
        integrity: sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==,
        tarball: https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.3.0.tgz,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  nullthrows@1.1.1:
    resolution:
      {
        integrity: sha512-2vPPEi+Z7WqML2jZYddDIfy5Dqb0r2fze2zTxNNknZaFpVHU3mFB3R+DWeJWGVx0ecvttSGlJTI+WG+8Z4cDWw==,
        tarball: https://registry.npmjs.org/nullthrows/-/nullthrows-1.1.1.tgz,
      }

  object-assign@4.1.1:
    resolution:
      {
        integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==,
        tarball: https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz,
      }
    engines: { node: '>=0.10.0' }

  object-hash@3.0.0:
    resolution:
      {
        integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==,
        tarball: https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz,
      }
    engines: { node: '>= 6' }

  object-inspect@1.13.4:
    resolution:
      {
        integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==,
        tarball: https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz,
      }
    engines: { node: '>= 0.4' }

  object-keys@1.1.1:
    resolution:
      {
        integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==,
        tarball: https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz,
      }
    engines: { node: '>= 0.4' }

  object.assign@4.1.7:
    resolution:
      {
        integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==,
        tarball: https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz,
      }
    engines: { node: '>= 0.4' }

  object.entries@1.1.9:
    resolution:
      {
        integrity: sha512-8u/hfXFRBD1O0hPUjioLhoWFHRmt6tKA4/vZPyckBr18l1KE9uHrFaFaUi8MDRTpi4uak2goyPTSNJLXX2k2Hw==,
        tarball: https://registry.npmjs.org/object.entries/-/object.entries-1.1.9.tgz,
      }
    engines: { node: '>= 0.4' }

  object.fromentries@2.0.8:
    resolution:
      {
        integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==,
        tarball: https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz,
      }
    engines: { node: '>= 0.4' }

  object.values@1.2.1:
    resolution:
      {
        integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==,
        tarball: https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz,
      }
    engines: { node: '>= 0.4' }

  once@1.4.0:
    resolution:
      {
        integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==,
        tarball: https://registry.npmjs.org/once/-/once-1.4.0.tgz,
      }

  onetime@5.1.2:
    resolution:
      {
        integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==,
        tarball: https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz,
      }
    engines: { node: '>=6' }

  onetime@6.0.0:
    resolution:
      {
        integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==,
        tarball: https://registry.npmjs.org/onetime/-/onetime-6.0.0.tgz,
      }
    engines: { node: '>=12' }

  optimism@0.18.1:
    resolution:
      {
        integrity: sha512-mLXNwWPa9dgFyDqkNi54sjDyNJ9/fTI6WGBLgnXku1vdKY/jovHfZT5r+aiVeFFLOz+foPNOm5YJ4mqgld2GBQ==,
        tarball: https://registry.npmjs.org/optimism/-/optimism-0.18.1.tgz,
      }

  optionator@0.9.4:
    resolution:
      {
        integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==,
        tarball: https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz,
      }
    engines: { node: '>= 0.8.0' }

  ora@5.4.1:
    resolution:
      {
        integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==,
        tarball: https://registry.npmjs.org/ora/-/ora-5.4.1.tgz,
      }
    engines: { node: '>=10' }

  os-tmpdir@1.0.2:
    resolution:
      {
        integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==,
        tarball: https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz,
      }
    engines: { node: '>=0.10.0' }

  own-keys@1.0.1:
    resolution:
      {
        integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==,
        tarball: https://registry.npmjs.org/own-keys/-/own-keys-1.0.1.tgz,
      }
    engines: { node: '>= 0.4' }

  p-limit@2.3.0:
    resolution:
      {
        integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==,
        tarball: https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz,
      }
    engines: { node: '>=6' }

  p-limit@3.1.0:
    resolution:
      {
        integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==,
        tarball: https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz,
      }
    engines: { node: '>=10' }

  p-limit@4.0.0:
    resolution:
      {
        integrity: sha512-5b0R4txpzjPWVw/cXXUResoD4hb6U/x9BH08L7nw+GN1sezDzPdxeRvpc9c433fZhBan/wusjbCsqwqm4EIBIQ==,
        tarball: https://registry.npmjs.org/p-limit/-/p-limit-4.0.0.tgz,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  p-locate@4.1.0:
    resolution:
      {
        integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==,
        tarball: https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz,
      }
    engines: { node: '>=8' }

  p-locate@5.0.0:
    resolution:
      {
        integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==,
        tarball: https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz,
      }
    engines: { node: '>=10' }

  p-locate@6.0.0:
    resolution:
      {
        integrity: sha512-wPrq66Llhl7/4AGC6I+cqxT07LhXvWL08LNXz1fENOw0Ap4sRZZ/gZpTTJ5jpurzzzfS2W/Ge9BY3LgLjCShcw==,
        tarball: https://registry.npmjs.org/p-locate/-/p-locate-6.0.0.tgz,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  p-map@4.0.0:
    resolution:
      {
        integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==,
        tarball: https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz,
      }
    engines: { node: '>=10' }

  p-try@2.2.0:
    resolution:
      {
        integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==,
        tarball: https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz,
      }
    engines: { node: '>=6' }

  package-json-from-dist@1.0.1:
    resolution:
      {
        integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==,
        tarball: https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz,
      }

  param-case@3.0.4:
    resolution:
      {
        integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==,
        tarball: https://registry.npmjs.org/param-case/-/param-case-3.0.4.tgz,
      }

  parent-module@1.0.1:
    resolution:
      {
        integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==,
        tarball: https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz,
      }
    engines: { node: '>=6' }

  parse-filepath@1.0.2:
    resolution:
      {
        integrity: sha512-FwdRXKCohSVeXqwtYonZTXtbGJKrn+HNyWDYVcp5yuJlesTwNH4rsmRZ+GrKAPJ5bLpRxESMeS+Rl0VCHRvB2Q==,
        tarball: https://registry.npmjs.org/parse-filepath/-/parse-filepath-1.0.2.tgz,
      }
    engines: { node: '>=0.8' }

  parse-json@5.2.0:
    resolution:
      {
        integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==,
        tarball: https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz,
      }
    engines: { node: '>=8' }

  pascal-case@3.1.2:
    resolution:
      {
        integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==,
        tarball: https://registry.npmjs.org/pascal-case/-/pascal-case-3.1.2.tgz,
      }

  path-case@3.0.4:
    resolution:
      {
        integrity: sha512-qO4qCFjXqVTrcbPt/hQfhTQ+VhFsqNKOPtytgNKkKxSoEp3XPUQ8ObFuePylOIok5gjn69ry8XiULxCwot3Wfg==,
        tarball: https://registry.npmjs.org/path-case/-/path-case-3.0.4.tgz,
      }

  path-exists@4.0.0:
    resolution:
      {
        integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==,
        tarball: https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz,
      }
    engines: { node: '>=8' }

  path-exists@5.0.0:
    resolution:
      {
        integrity: sha512-RjhtfwJOxzcFmNOi6ltcbcu4Iu+FL3zEj83dk4kAS+fVpTxXLO1b38RvJgT/0QwvV/L3aY9TAnyv0EOqW4GoMQ==,
        tarball: https://registry.npmjs.org/path-exists/-/path-exists-5.0.0.tgz,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  path-is-absolute@1.0.1:
    resolution:
      {
        integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==,
        tarball: https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz,
      }
    engines: { node: '>=0.10.0' }

  path-key@3.1.1:
    resolution:
      {
        integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==,
        tarball: https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz,
      }
    engines: { node: '>=8' }

  path-key@4.0.0:
    resolution:
      {
        integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==,
        tarball: https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz,
      }
    engines: { node: '>=12' }

  path-parse@1.0.7:
    resolution:
      {
        integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==,
        tarball: https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz,
      }

  path-root-regex@0.1.2:
    resolution:
      {
        integrity: sha512-4GlJ6rZDhQZFE0DPVKh0e9jmZ5egZfxTkp7bcRDuPlJXbAwhxcl2dINPUAsjLdejqaLsCeg8axcLjIbvBjN4pQ==,
        tarball: https://registry.npmjs.org/path-root-regex/-/path-root-regex-0.1.2.tgz,
      }
    engines: { node: '>=0.10.0' }

  path-root@0.1.1:
    resolution:
      {
        integrity: sha512-QLcPegTHF11axjfojBIoDygmS2E3Lf+8+jI6wOVmNVenrKSo3mFdSGiIgdSHenczw3wPtlVMQaFVwGmM7BJdtg==,
        tarball: https://registry.npmjs.org/path-root/-/path-root-0.1.1.tgz,
      }
    engines: { node: '>=0.10.0' }

  path-scurry@1.11.1:
    resolution:
      {
        integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==,
        tarball: https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz,
      }
    engines: { node: '>=16 || 14 >=14.18' }

  path-type@4.0.0:
    resolution:
      {
        integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==,
        tarball: https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz,
      }
    engines: { node: '>=8' }

  picocolors@1.1.1:
    resolution:
      {
        integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==,
        tarball: https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz,
      }

  picomatch@2.3.1:
    resolution:
      {
        integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==,
        tarball: https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz,
      }
    engines: { node: '>=8.6' }

  picomatch@4.0.2:
    resolution:
      {
        integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==,
        tarball: https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz,
      }
    engines: { node: '>=12' }

  pify@2.3.0:
    resolution:
      {
        integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==,
        tarball: https://registry.npmjs.org/pify/-/pify-2.3.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  pirates@4.0.7:
    resolution:
      {
        integrity: sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==,
        tarball: https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz,
      }
    engines: { node: '>= 6' }

  possible-typed-array-names@1.1.0:
    resolution:
      {
        integrity: sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==,
        tarball: https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  postcss-import@15.1.0:
    resolution:
      {
        integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==,
        tarball: https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz,
      }
    engines: { node: '>=14.0.0' }
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution:
      {
        integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==,
        tarball: https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz,
      }
    engines: { node: ^12 || ^14 || >= 16 }
    peerDependencies:
      postcss: ^8.4.21

  postcss-load-config@4.0.2:
    resolution:
      {
        integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==,
        tarball: https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz,
      }
    engines: { node: '>= 14' }
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-nested@6.2.0:
    resolution:
      {
        integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==,
        tarball: https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz,
      }
    engines: { node: '>=12.0' }
    peerDependencies:
      postcss: ^8.2.14

  postcss-selector-parser@6.1.2:
    resolution:
      {
        integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==,
        tarball: https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz,
      }
    engines: { node: '>=4' }

  postcss-value-parser@4.2.0:
    resolution:
      {
        integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==,
        tarball: https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz,
      }

  postcss@8.5.0:
    resolution:
      {
        integrity: sha512-27VKOqrYfPncKA2NrFOVhP5MGAfHKLYn/Q0mz9cNQyRAKYi3VNHwYU2qKKqPCqgBmeeJ0uAFB56NumXZ5ZReXg==,
        tarball: https://registry.npmjs.org/postcss/-/postcss-8.5.0.tgz,
      }
    engines: { node: ^10 || ^12 || >=14 }

  posthog-js@1.249.3:
    resolution:
      {
        integrity: sha512-+DMUJYADNakPU05tGdJFoWbpyb5gDwRczT2+gFI72Th3315Wl+k7hKwBpzq2zvH5+jKOHjE0SKD/fzf1AFInkw==,
        tarball: https://registry.npmjs.org/posthog-js/-/posthog-js-1.249.3.tgz,
      }
    peerDependencies:
      '@rrweb/types': 2.0.0-alpha.17
      rrweb-snapshot: 2.0.0-alpha.17
    peerDependenciesMeta:
      '@rrweb/types':
        optional: true
      rrweb-snapshot:
        optional: true

  preact@10.26.8:
    resolution:
      {
        integrity: sha512-1nMfdFjucm5hKvq0IClqZwK4FJkGXhRrQstOQ3P4vp8HxKrJEMFcY6RdBRVTdfQS/UlnX6gfbPuTvaqx/bDoeQ==,
        tarball: https://registry.npmjs.org/preact/-/preact-10.26.8.tgz,
      }

  prelude-ls@1.2.1:
    resolution:
      {
        integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==,
        tarball: https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz,
      }
    engines: { node: '>= 0.8.0' }

  prettier-linter-helpers@1.0.0:
    resolution:
      {
        integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==,
        tarball: https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz,
      }
    engines: { node: '>=6.0.0' }

  prettier@3.5.3:
    resolution:
      {
        integrity: sha512-QQtaxnoDJeAkDvDKWCLiwIXkTgRhwYDEQCghU9Z6q03iyek/rxRh/2lC3HB7P8sWT2xC/y5JDctPLBIGzHKbhw==,
        tarball: https://registry.npmjs.org/prettier/-/prettier-3.5.3.tgz,
      }
    engines: { node: '>=14' }
    hasBin: true

  pretty-format@29.7.0:
    resolution:
      {
        integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==,
        tarball: https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  progress@2.0.3:
    resolution:
      {
        integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==,
        tarball: https://registry.npmjs.org/progress/-/progress-2.0.3.tgz,
      }
    engines: { node: '>=0.4.0' }

  promise@7.3.1:
    resolution:
      {
        integrity: sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==,
        tarball: https://registry.npmjs.org/promise/-/promise-7.3.1.tgz,
      }

  prop-types@15.8.1:
    resolution:
      {
        integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==,
        tarball: https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz,
      }

  proxy-from-env@1.1.0:
    resolution:
      {
        integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==,
        tarball: https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz,
      }

  punycode@1.4.1:
    resolution:
      {
        integrity: sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==,
        tarball: https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz,
      }

  punycode@2.3.1:
    resolution:
      {
        integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==,
        tarball: https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz,
      }
    engines: { node: '>=6' }

  pvtsutils@1.3.6:
    resolution:
      {
        integrity: sha512-PLgQXQ6H2FWCaeRak8vvk1GW462lMxB5s3Jm673N82zI4vqtVUPuZdffdZbPDFRoU8kAhItWFtPCWiPpp4/EDg==,
        tarball: https://registry.npmjs.org/pvtsutils/-/pvtsutils-1.3.6.tgz,
      }

  pvutils@1.1.3:
    resolution:
      {
        integrity: sha512-pMpnA0qRdFp32b1sJl1wOJNxZLQ2cbQx+k6tjNtZ8CpvVhNqEPRgivZ2WOUev2YMajecdH7ctUPDvEe87nariQ==,
        tarball: https://registry.npmjs.org/pvutils/-/pvutils-1.1.3.tgz,
      }
    engines: { node: '>=6.0.0' }

  queue-microtask@1.2.3:
    resolution:
      {
        integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==,
        tarball: https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz,
      }

  react-dom@18.2.0:
    resolution:
      {
        integrity: sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==,
        tarball: https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz,
      }
    peerDependencies:
      react: ^18.2.0

  react-fast-compare@3.2.2:
    resolution:
      {
        integrity: sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==,
        tarball: https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-3.2.2.tgz,
      }

  react-helmet-async@2.0.5:
    resolution:
      {
        integrity: sha512-rYUYHeus+i27MvFE+Jaa4WsyBKGkL6qVgbJvSBoX8mbsWoABJXdEO0bZyi0F6i+4f0NuIb8AvqPMj3iXFHkMwg==,
        tarball: https://registry.npmjs.org/react-helmet-async/-/react-helmet-async-2.0.5.tgz,
      }
    peerDependencies:
      react: ^16.6.0 || ^17.0.0 || ^18.0.0

  react-hook-form@7.51.1:
    resolution:
      {
        integrity: sha512-ifnBjl+kW0ksINHd+8C/Gp6a4eZOdWyvRv0UBaByShwU8JbVx5hTcTWEcd5VdybvmPTATkVVXk9npXArHmo56w==,
        tarball: https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.51.1.tgz,
      }
    engines: { node: '>=12.22.0' }
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18

  react-i18next@14.1.0:
    resolution:
      {
        integrity: sha512-3KwX6LHpbvGQ+sBEntjV4sYW3Zovjjl3fpoHbUwSgFHf0uRBcbeCBLR5al6ikncI5+W0EFb71QXZmfop+J6NrQ==,
        tarball: https://registry.npmjs.org/react-i18next/-/react-i18next-14.1.0.tgz,
      }
    peerDependencies:
      i18next: '>= 23.2.3'
      react: '>= 16.8.0'
      react-dom: '*'
      react-native: '*'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true

  react-is@16.13.1:
    resolution:
      {
        integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==,
        tarball: https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz,
      }

  react-is@18.3.1:
    resolution:
      {
        integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==,
        tarball: https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz,
      }

  react-loading-skeleton@3.4.0:
    resolution:
      {
        integrity: sha512-1oJEBc9+wn7BbkQQk7YodlYEIjgeR+GrRjD+QXkVjwZN7LGIcAFHrx4NhT7UHGBxNY1+zax3c+Fo6XQM4R7CgA==,
        tarball: https://registry.npmjs.org/react-loading-skeleton/-/react-loading-skeleton-3.4.0.tgz,
      }
    peerDependencies:
      react: '>=16.8.0'

  react-number-format@5.4.3:
    resolution:
      {
        integrity: sha512-VCY5hFg/soBighAoGcdE+GagkJq0230qN6jcS5sp8wQX1qy1fYN/RX7/BXkrs0oyzzwqR8/+eSUrqXbGeywdUQ==,
        tarball: https://registry.npmjs.org/react-number-format/-/react-number-format-5.4.3.tgz,
      }
    peerDependencies:
      react: ^0.14 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^0.14 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  react-popper-tooltip@4.4.2:
    resolution:
      {
        integrity: sha512-y48r0mpzysRTZAIh8m2kpZ8S1YPNqGtQPDrlXYSGvDS1c1GpG/NUXbsbIdfbhXfmSaRJuTcaT6N1q3CKuHRVbg==,
        tarball: https://registry.npmjs.org/react-popper-tooltip/-/react-popper-tooltip-4.4.2.tgz,
      }
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react-popper@2.3.0:
    resolution:
      {
        integrity: sha512-e1hj8lL3uM+sgSR4Lxzn5h1GxBlpa4CQz0XLF8kx4MDrDRWY0Ena4c97PUeSX9i5W3UAfDP0z0FXCTQkoXUl3Q==,
        tarball: https://registry.npmjs.org/react-popper/-/react-popper-2.3.0.tgz,
      }
    peerDependencies:
      '@popperjs/core': ^2.0.0
      react: ^16.8.0 || ^17 || ^18
      react-dom: ^16.8.0 || ^17 || ^18

  react-remove-scroll-bar@2.3.8:
    resolution:
      {
        integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==,
        tarball: https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz,
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.7.1:
    resolution:
      {
        integrity: sha512-HpMh8+oahmIdOuS5aFKKY6Pyog+FNaZV/XyJOq7b4YFwsFHe5yYfdbIalI4k3vU2nSDql7YskmUseHsRrJqIPA==,
        tarball: https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.7.1.tgz,
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-router-dom@6.22.3:
    resolution:
      {
        integrity: sha512-7ZILI7HjcE+p31oQvwbokjk6OA/bnFxrhJ19n82Ex9Ph8fNAq+Hm/7KchpMGlTgWhUxRHMMCut+vEtNpWpowKw==,
        tarball: https://registry.npmjs.org/react-router-dom/-/react-router-dom-6.22.3.tgz,
      }
    engines: { node: '>=14.0.0' }
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'

  react-router@6.22.3:
    resolution:
      {
        integrity: sha512-dr2eb3Mj5zK2YISHK++foM9w4eBnO23eKnZEDs7c880P6oKbrjz/Svg9+nxqtHQK+oMW4OtjZca0RqPglXxguQ==,
        tarball: https://registry.npmjs.org/react-router/-/react-router-6.22.3.tgz,
      }
    engines: { node: '>=14.0.0' }
    peerDependencies:
      react: '>=16.8'

  react-style-singleton@2.2.3:
    resolution:
      {
        integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==,
        tarball: https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz,
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-toastify@10.0.5:
    resolution:
      {
        integrity: sha512-mNKt2jBXJg4O7pSdbNUfDdTsK9FIdikfsIE/yUCxbAEXl4HMyJaivrVFcn3Elvt5xvCQYhUZm+hqTIu1UXM3Pw==,
        tarball: https://registry.npmjs.org/react-toastify/-/react-toastify-10.0.5.tgz,
      }
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'

  react-universal-interface@0.6.2:
    resolution:
      {
        integrity: sha512-dg8yXdcQmvgR13RIlZbTRQOoUrDciFVoSBZILwjE2LFISxZZ8loVJKAkuzswl5js8BHda79bIb2b84ehU8IjXw==,
        tarball: https://registry.npmjs.org/react-universal-interface/-/react-universal-interface-0.6.2.tgz,
      }
    peerDependencies:
      react: '*'
      tslib: '*'

  react-use@17.5.1:
    resolution:
      {
        integrity: sha512-LG/uPEVRflLWMwi3j/sZqR00nF6JGqTTDblkXK2nzXsIvij06hXl1V/MZIlwj1OKIQUtlh1l9jK8gLsRyCQxMg==,
        tarball: https://registry.npmjs.org/react-use/-/react-use-17.5.1.tgz,
      }
    peerDependencies:
      react: '*'
      react-dom: '*'

  react@18.2.0:
    resolution:
      {
        integrity: sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==,
        tarball: https://registry.npmjs.org/react/-/react-18.2.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  read-cache@1.0.0:
    resolution:
      {
        integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==,
        tarball: https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz,
      }

  readable-stream@3.6.2:
    resolution:
      {
        integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==,
        tarball: https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz,
      }
    engines: { node: '>= 6' }

  readdirp@3.6.0:
    resolution:
      {
        integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==,
        tarball: https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz,
      }
    engines: { node: '>=8.10.0' }

  reflect.getprototypeof@1.0.10:
    resolution:
      {
        integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==,
        tarball: https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz,
      }
    engines: { node: '>= 0.4' }

  regexp.prototype.flags@1.5.4:
    resolution:
      {
        integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==,
        tarball: https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz,
      }
    engines: { node: '>= 0.4' }

  rehackt@0.0.6:
    resolution:
      {
        integrity: sha512-l3WEzkt4ntlEc/IB3/mF6SRgNHA6zfQR7BlGOgBTOmx7IJJXojDASav+NsgXHFjHn+6RmwqsGPFgZpabWpeOdw==,
        tarball: https://registry.npmjs.org/rehackt/-/rehackt-0.0.6.tgz,
      }
    peerDependencies:
      '@types/react': '*'
      react: '*'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      react:
        optional: true

  relay-runtime@12.0.0:
    resolution:
      {
        integrity: sha512-QU6JKr1tMsry22DXNy9Whsq5rmvwr3LSZiiWV/9+DFpuTWvp+WFhobWMc8TC4OjKFfNhEZy7mOiqUAn5atQtug==,
        tarball: https://registry.npmjs.org/relay-runtime/-/relay-runtime-12.0.0.tgz,
      }

  remedial@1.0.8:
    resolution:
      {
        integrity: sha512-/62tYiOe6DzS5BqVsNpH/nkGlX45C/Sp6V+NtiN6JQNS1Viay7cWkazmRkrQrdFj2eshDe96SIQNIoMxqhzBOg==,
        tarball: https://registry.npmjs.org/remedial/-/remedial-1.0.8.tgz,
      }

  remove-trailing-separator@1.1.0:
    resolution:
      {
        integrity: sha512-/hS+Y0u3aOfIETiaiirUFwDBDzmXPvO+jAfKTitUngIPzdKc6Z0LoFjM/CK5PL4C+eKwHohlHAb6H0VFfmmUsw==,
        tarball: https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz,
      }

  remove-trailing-spaces@1.0.9:
    resolution:
      {
        integrity: sha512-xzG7w5IRijvIkHIjDk65URsJJ7k4J95wmcArY5PRcmjldIOl7oTvG8+X2Ag690R7SfwiOcHrWZKVc1Pp5WIOzA==,
        tarball: https://registry.npmjs.org/remove-trailing-spaces/-/remove-trailing-spaces-1.0.9.tgz,
      }

  require-directory@2.1.1:
    resolution:
      {
        integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==,
        tarball: https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz,
      }
    engines: { node: '>=0.10.0' }

  require-from-string@2.0.2:
    resolution:
      {
        integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==,
        tarball: https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz,
      }
    engines: { node: '>=0.10.0' }

  require-main-filename@2.0.0:
    resolution:
      {
        integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==,
        tarball: https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz,
      }

  resize-observer-polyfill@1.5.1:
    resolution:
      {
        integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==,
        tarball: https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz,
      }

  resolve-from@4.0.0:
    resolution:
      {
        integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==,
        tarball: https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz,
      }
    engines: { node: '>=4' }

  resolve-from@5.0.0:
    resolution:
      {
        integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==,
        tarball: https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz,
      }
    engines: { node: '>=8' }

  resolve@1.22.10:
    resolution:
      {
        integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==,
        tarball: https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz,
      }
    engines: { node: '>= 0.4' }
    hasBin: true

  resolve@2.0.0-next.5:
    resolution:
      {
        integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==,
        tarball: https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz,
      }
    hasBin: true

  response-iterator@0.2.22:
    resolution:
      {
        integrity: sha512-+Hjn4hO56dKyEthXS5mIFQPIpAi7zL5+ScuSlPTU+hp1EXj0GbiWxdEXl3comWlpOiaysAFG45TzH2qj5AZ44g==,
        tarball: https://registry.npmjs.org/response-iterator/-/response-iterator-0.2.22.tgz,
      }
    engines: { node: '>=0.8' }

  restore-cursor@3.1.0:
    resolution:
      {
        integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==,
        tarball: https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz,
      }
    engines: { node: '>=8' }

  reusify@1.1.0:
    resolution:
      {
        integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==,
        tarball: https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz,
      }
    engines: { iojs: '>=1.0.0', node: '>=0.10.0' }

  rfdc@1.4.1:
    resolution:
      {
        integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==,
        tarball: https://registry.npmjs.org/rfdc/-/rfdc-1.4.1.tgz,
      }

  rollup@4.41.1:
    resolution:
      {
        integrity: sha512-cPmwD3FnFv8rKMBc1MxWCwVQFxwf1JEmSX3iQXrRVVG15zerAIXRjMFVWnd5Q5QvgKF7Aj+5ykXFhUl+QGnyOw==,
        tarball: https://registry.npmjs.org/rollup/-/rollup-4.41.1.tgz,
      }
    engines: { node: '>=18.0.0', npm: '>=8.0.0' }
    hasBin: true

  rtl-css-js@1.16.1:
    resolution:
      {
        integrity: sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==,
        tarball: https://registry.npmjs.org/rtl-css-js/-/rtl-css-js-1.16.1.tgz,
      }

  run-async@2.4.1:
    resolution:
      {
        integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==,
        tarball: https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz,
      }
    engines: { node: '>=0.12.0' }

  run-parallel@1.2.0:
    resolution:
      {
        integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==,
        tarball: https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz,
      }

  rxjs@7.8.2:
    resolution:
      {
        integrity: sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==,
        tarball: https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz,
      }

  safe-array-concat@1.1.3:
    resolution:
      {
        integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==,
        tarball: https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz,
      }
    engines: { node: '>=0.4' }

  safe-buffer@5.2.1:
    resolution:
      {
        integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==,
        tarball: https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz,
      }

  safe-push-apply@1.0.0:
    resolution:
      {
        integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==,
        tarball: https://registry.npmjs.org/safe-push-apply/-/safe-push-apply-1.0.0.tgz,
      }
    engines: { node: '>= 0.4' }

  safe-regex-test@1.1.0:
    resolution:
      {
        integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==,
        tarball: https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  safer-buffer@2.1.2:
    resolution:
      {
        integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==,
        tarball: https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz,
      }

  sass@1.72.0:
    resolution:
      {
        integrity: sha512-Gpczt3WA56Ly0Mn8Sl21Vj94s1axi9hDIzDFn9Ph9x3C3p4nNyvsqJoQyVXKou6cBlfFWEgRW4rT8Tb4i3XnVA==,
        tarball: https://registry.npmjs.org/sass/-/sass-1.72.0.tgz,
      }
    engines: { node: '>=14.0.0' }
    hasBin: true

  scheduler@0.23.2:
    resolution:
      {
        integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==,
        tarball: https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz,
      }

  screenfull@5.2.0:
    resolution:
      {
        integrity: sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==,
        tarball: https://registry.npmjs.org/screenfull/-/screenfull-5.2.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  scuid@1.1.0:
    resolution:
      {
        integrity: sha512-MuCAyrGZcTLfQoH2XoBlQ8C6bzwN88XT/0slOGz0pn8+gIP85BOAfYa44ZXQUTOwRwPU0QvgU+V+OSajl/59Xg==,
        tarball: https://registry.npmjs.org/scuid/-/scuid-1.1.0.tgz,
      }

  semver@6.3.1:
    resolution:
      {
        integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==,
        tarball: https://registry.npmjs.org/semver/-/semver-6.3.1.tgz,
      }
    hasBin: true

  semver@7.7.2:
    resolution:
      {
        integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==,
        tarball: https://registry.npmjs.org/semver/-/semver-7.7.2.tgz,
      }
    engines: { node: '>=10' }
    hasBin: true

  sentence-case@3.0.4:
    resolution:
      {
        integrity: sha512-8LS0JInaQMCRoQ7YUytAo/xUu5W2XnQxV2HI/6uM6U7CITS1RqPElr30V6uIqyMKM9lJGRVFy5/4CuzcixNYSg==,
        tarball: https://registry.npmjs.org/sentence-case/-/sentence-case-3.0.4.tgz,
      }

  set-blocking@2.0.0:
    resolution:
      {
        integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==,
        tarball: https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz,
      }

  set-function-length@1.2.2:
    resolution:
      {
        integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==,
        tarball: https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz,
      }
    engines: { node: '>= 0.4' }

  set-function-name@2.0.2:
    resolution:
      {
        integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==,
        tarball: https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  set-harmonic-interval@1.0.1:
    resolution:
      {
        integrity: sha512-AhICkFV84tBP1aWqPwLZqFvAwqEoVA9kxNMniGEUvzOlm4vLmOFLiTT3UZ6bziJTy4bOVpzWGTfSCbmaayGx8g==,
        tarball: https://registry.npmjs.org/set-harmonic-interval/-/set-harmonic-interval-1.0.1.tgz,
      }
    engines: { node: '>=6.9' }

  set-proto@1.0.0:
    resolution:
      {
        integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==,
        tarball: https://registry.npmjs.org/set-proto/-/set-proto-1.0.0.tgz,
      }
    engines: { node: '>= 0.4' }

  setimmediate@1.0.5:
    resolution:
      {
        integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==,
        tarball: https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz,
      }

  shallowequal@1.1.0:
    resolution:
      {
        integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==,
        tarball: https://registry.npmjs.org/shallowequal/-/shallowequal-1.1.0.tgz,
      }

  shebang-command@2.0.0:
    resolution:
      {
        integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==,
        tarball: https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz,
      }
    engines: { node: '>=8' }

  shebang-regex@3.0.0:
    resolution:
      {
        integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==,
        tarball: https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz,
      }
    engines: { node: '>=8' }

  shell-quote@1.8.3:
    resolution:
      {
        integrity: sha512-ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw==,
        tarball: https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz,
      }
    engines: { node: '>= 0.4' }

  side-channel-list@1.0.0:
    resolution:
      {
        integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==,
        tarball: https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz,
      }
    engines: { node: '>= 0.4' }

  side-channel-map@1.0.1:
    resolution:
      {
        integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==,
        tarball: https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz,
      }
    engines: { node: '>= 0.4' }

  side-channel-weakmap@1.0.2:
    resolution:
      {
        integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==,
        tarball: https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  side-channel@1.1.0:
    resolution:
      {
        integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==,
        tarball: https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  signal-exit@3.0.7:
    resolution:
      {
        integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==,
        tarball: https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz,
      }

  signal-exit@4.1.0:
    resolution:
      {
        integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==,
        tarball: https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz,
      }
    engines: { node: '>=14' }

  signedsource@1.0.0:
    resolution:
      {
        integrity: sha512-6+eerH9fEnNmi/hyM1DXcRK3pWdoMQtlkQ+ns0ntzunjKqp5i3sKCc80ym8Fib3iaYhdJUOPdhlJWj1tvge2Ww==,
        tarball: https://registry.npmjs.org/signedsource/-/signedsource-1.0.0.tgz,
      }

  slash@3.0.0:
    resolution:
      {
        integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==,
        tarball: https://registry.npmjs.org/slash/-/slash-3.0.0.tgz,
      }
    engines: { node: '>=8' }

  slice-ansi@3.0.0:
    resolution:
      {
        integrity: sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ==,
        tarball: https://registry.npmjs.org/slice-ansi/-/slice-ansi-3.0.0.tgz,
      }
    engines: { node: '>=8' }

  slice-ansi@4.0.0:
    resolution:
      {
        integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==,
        tarball: https://registry.npmjs.org/slice-ansi/-/slice-ansi-4.0.0.tgz,
      }
    engines: { node: '>=10' }

  snake-case@3.0.4:
    resolution:
      {
        integrity: sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==,
        tarball: https://registry.npmjs.org/snake-case/-/snake-case-3.0.4.tgz,
      }

  sonner@2.0.1:
    resolution:
      {
        integrity: sha512-FRBphaehZ5tLdLcQ8g2WOIRE+Y7BCfWi5Zyd8bCvBjiW8TxxAyoWZIxS661Yz6TGPqFQ4VLzOF89WEYhfynSFQ==,
        tarball: https://registry.npmjs.org/sonner/-/sonner-2.0.1.tgz,
      }
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  source-map-js@1.2.1:
    resolution:
      {
        integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==,
        tarball: https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz,
      }
    engines: { node: '>=0.10.0' }

  source-map@0.5.6:
    resolution:
      {
        integrity: sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA==,
        tarball: https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz,
      }
    engines: { node: '>=0.10.0' }

  source-map@0.6.1:
    resolution:
      {
        integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==,
        tarball: https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz,
      }
    engines: { node: '>=0.10.0' }

  split2@4.2.0:
    resolution:
      {
        integrity: sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==,
        tarball: https://registry.npmjs.org/split2/-/split2-4.2.0.tgz,
      }
    engines: { node: '>= 10.x' }

  sponge-case@1.0.1:
    resolution:
      {
        integrity: sha512-dblb9Et4DAtiZ5YSUZHLl4XhH4uK80GhAZrVXdN4O2P4gQ40Wa5UIOPUHlA/nFd2PLblBZWUioLMMAVrgpoYcA==,
        tarball: https://registry.npmjs.org/sponge-case/-/sponge-case-1.0.1.tgz,
      }

  stack-generator@2.0.10:
    resolution:
      {
        integrity: sha512-mwnua/hkqM6pF4k8SnmZ2zfETsRUpWXREfA/goT8SLCV4iOFa4bzOX2nDipWAZFPTjLvQB82f5yaodMVhK0yJQ==,
        tarball: https://registry.npmjs.org/stack-generator/-/stack-generator-2.0.10.tgz,
      }

  stack-utils@2.0.6:
    resolution:
      {
        integrity: sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==,
        tarball: https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz,
      }
    engines: { node: '>=10' }

  stackframe@1.3.4:
    resolution:
      {
        integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==,
        tarball: https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz,
      }

  stacktrace-gps@3.1.2:
    resolution:
      {
        integrity: sha512-GcUgbO4Jsqqg6RxfyTHFiPxdPqF+3LFmQhm7MgCuYQOYuWyqxo5pwRPz5d/u6/WYJdEnWfK4r+jGbyD8TSggXQ==,
        tarball: https://registry.npmjs.org/stacktrace-gps/-/stacktrace-gps-3.1.2.tgz,
      }

  stacktrace-js@2.0.2:
    resolution:
      {
        integrity: sha512-Je5vBeY4S1r/RnLydLl0TBTi3F2qdfWmYsGvtfZgEI+SCprPppaIhQf5nGcal4gI4cGpCV/duLcAzT1np6sQqg==,
        tarball: https://registry.npmjs.org/stacktrace-js/-/stacktrace-js-2.0.2.tgz,
      }

  stop-iteration-iterator@1.1.0:
    resolution:
      {
        integrity: sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==,
        tarball: https://registry.npmjs.org/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  streamsearch@1.1.0:
    resolution:
      {
        integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==,
        tarball: https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz,
      }
    engines: { node: '>=10.0.0' }

  string-env-interpolation@1.0.1:
    resolution:
      {
        integrity: sha512-78lwMoCcn0nNu8LszbP1UA7g55OeE4v7rCeWnM5B453rnNr4aq+5it3FEYtZrSEiMvHZOZ9Jlqb0OD0M2VInqg==,
        tarball: https://registry.npmjs.org/string-env-interpolation/-/string-env-interpolation-1.0.1.tgz,
      }

  string-width@4.2.3:
    resolution:
      {
        integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==,
        tarball: https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz,
      }
    engines: { node: '>=8' }

  string-width@5.1.2:
    resolution:
      {
        integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==,
        tarball: https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz,
      }
    engines: { node: '>=12' }

  string.prototype.matchall@4.0.12:
    resolution:
      {
        integrity: sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==,
        tarball: https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz,
      }
    engines: { node: '>= 0.4' }

  string.prototype.repeat@1.0.0:
    resolution:
      {
        integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==,
        tarball: https://registry.npmjs.org/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz,
      }

  string.prototype.trim@1.2.10:
    resolution:
      {
        integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==,
        tarball: https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz,
      }
    engines: { node: '>= 0.4' }

  string.prototype.trimend@1.0.9:
    resolution:
      {
        integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==,
        tarball: https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz,
      }
    engines: { node: '>= 0.4' }

  string.prototype.trimstart@1.0.8:
    resolution:
      {
        integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==,
        tarball: https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz,
      }
    engines: { node: '>= 0.4' }

  string_decoder@1.3.0:
    resolution:
      {
        integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==,
        tarball: https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz,
      }

  strip-ansi@6.0.1:
    resolution:
      {
        integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==,
        tarball: https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz,
      }
    engines: { node: '>=8' }

  strip-ansi@7.1.0:
    resolution:
      {
        integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==,
        tarball: https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz,
      }
    engines: { node: '>=12' }

  strip-final-newline@3.0.0:
    resolution:
      {
        integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==,
        tarball: https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-3.0.0.tgz,
      }
    engines: { node: '>=12' }

  strip-json-comments@3.1.1:
    resolution:
      {
        integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==,
        tarball: https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz,
      }
    engines: { node: '>=8' }

  stylis@4.3.6:
    resolution:
      {
        integrity: sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==,
        tarball: https://registry.npmjs.org/stylis/-/stylis-4.3.6.tgz,
      }

  sucrase@3.35.0:
    resolution:
      {
        integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==,
        tarball: https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz,
      }
    engines: { node: '>=16 || 14 >=14.17' }
    hasBin: true

  supports-color@7.2.0:
    resolution:
      {
        integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==,
        tarball: https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz,
      }
    engines: { node: '>=8' }

  supports-preserve-symlinks-flag@1.0.0:
    resolution:
      {
        integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==,
        tarball: https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz,
      }
    engines: { node: '>= 0.4' }

  svg-parser@2.0.4:
    resolution:
      {
        integrity: sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ==,
        tarball: https://registry.npmjs.org/svg-parser/-/svg-parser-2.0.4.tgz,
      }

  swap-case@2.0.2:
    resolution:
      {
        integrity: sha512-kc6S2YS/2yXbtkSMunBtKdah4VFETZ8Oh6ONSmSd9bRxhqTrtARUCBUiWXH3xVPpvR7tz2CSnkuXVE42EcGnMw==,
        tarball: https://registry.npmjs.org/swap-case/-/swap-case-2.0.2.tgz,
      }

  symbol-observable@4.0.0:
    resolution:
      {
        integrity: sha512-b19dMThMV4HVFynSAM1++gBHAbk2Tc/osgLIBZMKsyqh34jb2e8Os7T6ZW/Bt3pJFdBTd2JwAnAAEQV7rSNvcQ==,
        tarball: https://registry.npmjs.org/symbol-observable/-/symbol-observable-4.0.0.tgz,
      }
    engines: { node: '>=0.10' }

  sync-fetch@0.6.0-2:
    resolution:
      {
        integrity: sha512-c7AfkZ9udatCuAy9RSfiGPpeOKKUAUK5e1cXadLOGUjasdxqYqAK0jTNkM/FSEyJ3a5Ra27j/tw/PS0qLmaF/A==,
        tarball: https://registry.npmjs.org/sync-fetch/-/sync-fetch-0.6.0-2.tgz,
      }
    engines: { node: '>=18' }

  synckit@0.11.8:
    resolution:
      {
        integrity: sha512-+XZ+r1XGIJGeQk3VvXhT6xx/VpbHsRzsTkGgF6E5RX9TTXD0118l87puaEBZ566FhqblC6U0d4XnubznJDm30A==,
        tarball: https://registry.npmjs.org/synckit/-/synckit-0.11.8.tgz,
      }
    engines: { node: ^14.18.0 || >=16.0.0 }

  tailwind-merge@2.6.0:
    resolution:
      {
        integrity: sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==,
        tarball: https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-2.6.0.tgz,
      }

  tailwindcss-animate@1.0.7:
    resolution:
      {
        integrity: sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==,
        tarball: https://registry.npmjs.org/tailwindcss-animate/-/tailwindcss-animate-1.0.7.tgz,
      }
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders'

  tailwindcss@3.4.17:
    resolution:
      {
        integrity: sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==,
        tarball: https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.17.tgz,
      }
    engines: { node: '>=14.0.0' }
    hasBin: true

  text-extensions@2.4.0:
    resolution:
      {
        integrity: sha512-te/NtwBwfiNRLf9Ijqx3T0nlqZiQ2XrrtBvu+cLL8ZRrGkO0NHTug8MYFKyoSrv/sHTaSKfilUkizV6XhxMJ3g==,
        tarball: https://registry.npmjs.org/text-extensions/-/text-extensions-2.4.0.tgz,
      }
    engines: { node: '>=8' }

  thenify-all@1.6.0:
    resolution:
      {
        integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==,
        tarball: https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz,
      }
    engines: { node: '>=0.8' }

  thenify@3.3.1:
    resolution:
      {
        integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==,
        tarball: https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz,
      }

  throttle-debounce@3.0.1:
    resolution:
      {
        integrity: sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==,
        tarball: https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-3.0.1.tgz,
      }
    engines: { node: '>=10' }

  through@2.3.8:
    resolution:
      {
        integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==,
        tarball: https://registry.npmjs.org/through/-/through-2.3.8.tgz,
      }

  timeout-signal@2.0.0:
    resolution:
      {
        integrity: sha512-YBGpG4bWsHoPvofT6y/5iqulfXIiIErl5B0LdtHT1mGXDFTAhhRrbUpTvBgYbovr+3cKblya2WAOcpoy90XguA==,
        tarball: https://registry.npmjs.org/timeout-signal/-/timeout-signal-2.0.0.tgz,
      }
    engines: { node: '>=16' }

  tinyexec@1.0.1:
    resolution:
      {
        integrity: sha512-5uC6DDlmeqiOwCPmK9jMSdOuZTh8bU39Ys6yidB+UTt5hfZUPGAypSgFRiEp+jbi9qH40BLDvy85jIU88wKSqw==,
        tarball: https://registry.npmjs.org/tinyexec/-/tinyexec-1.0.1.tgz,
      }

  title-case@3.0.3:
    resolution:
      {
        integrity: sha512-e1zGYRvbffpcHIrnuqT0Dh+gEJtDaxDSoG4JAIpq4oDFyooziLBIiYQv0GBT4FUAnUop5uZ1hiIAj7oAF6sOCA==,
        tarball: https://registry.npmjs.org/title-case/-/title-case-3.0.3.tgz,
      }

  tmp@0.0.33:
    resolution:
      {
        integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==,
        tarball: https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz,
      }
    engines: { node: '>=0.6.0' }

  to-regex-range@5.0.1:
    resolution:
      {
        integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==,
        tarball: https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz,
      }
    engines: { node: '>=8.0' }

  toggle-selection@1.0.6:
    resolution:
      {
        integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==,
        tarball: https://registry.npmjs.org/toggle-selection/-/toggle-selection-1.0.6.tgz,
      }

  tr46@0.0.3:
    resolution:
      {
        integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==,
        tarball: https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz,
      }

  ts-api-utils@2.1.0:
    resolution:
      {
        integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==,
        tarball: https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-2.1.0.tgz,
      }
    engines: { node: '>=18.12' }
    peerDependencies:
      typescript: '>=4.8.4'

  ts-easing@0.2.0:
    resolution:
      {
        integrity: sha512-Z86EW+fFFh/IFB1fqQ3/+7Zpf9t2ebOAxNI/V6Wo7r5gqiqtxmgTlQ1qbqQcjLKYeSHPTsEmvlJUDg/EuL0uHQ==,
        tarball: https://registry.npmjs.org/ts-easing/-/ts-easing-0.2.0.tgz,
      }

  ts-interface-checker@0.1.13:
    resolution:
      {
        integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==,
        tarball: https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz,
      }

  ts-invariant@0.10.3:
    resolution:
      {
        integrity: sha512-uivwYcQaxAucv1CzRp2n/QdYPo4ILf9VXgH19zEIjFx2EJufV16P0JtJVpYHy89DItG6Kwj2oIUjrcK5au+4tQ==,
        tarball: https://registry.npmjs.org/ts-invariant/-/ts-invariant-0.10.3.tgz,
      }
    engines: { node: '>=8' }

  ts-log@2.2.7:
    resolution:
      {
        integrity: sha512-320x5Ggei84AxzlXp91QkIGSw5wgaLT6GeAH0KsqDmRZdVWW2OiSeVvElVoatk3f7nicwXlElXsoFkARiGE2yg==,
        tarball: https://registry.npmjs.org/ts-log/-/ts-log-2.2.7.tgz,
      }

  tsconfck@3.1.6:
    resolution:
      {
        integrity: sha512-ks6Vjr/jEw0P1gmOVwutM3B7fWxoWBL2KRDb1JfqGVawBmO5UsvmWOQFGHBPl5yxYz4eERr19E6L7NMv+Fej4w==,
        tarball: https://registry.npmjs.org/tsconfck/-/tsconfck-3.1.6.tgz,
      }
    engines: { node: ^18 || >=20 }
    hasBin: true
    peerDependencies:
      typescript: ^5.0.0
    peerDependenciesMeta:
      typescript:
        optional: true

  tslib@2.4.1:
    resolution:
      {
        integrity: sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==,
        tarball: https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz,
      }

  tslib@2.6.3:
    resolution:
      {
        integrity: sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==,
        tarball: https://registry.npmjs.org/tslib/-/tslib-2.6.3.tgz,
      }

  tslib@2.8.1:
    resolution:
      {
        integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==,
        tarball: https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz,
      }

  type-check@0.4.0:
    resolution:
      {
        integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==,
        tarball: https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz,
      }
    engines: { node: '>= 0.8.0' }

  type-fest@0.21.3:
    resolution:
      {
        integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==,
        tarball: https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz,
      }
    engines: { node: '>=10' }

  typed-array-buffer@1.0.3:
    resolution:
      {
        integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==,
        tarball: https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz,
      }
    engines: { node: '>= 0.4' }

  typed-array-byte-length@1.0.3:
    resolution:
      {
        integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==,
        tarball: https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz,
      }
    engines: { node: '>= 0.4' }

  typed-array-byte-offset@1.0.4:
    resolution:
      {
        integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==,
        tarball: https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz,
      }
    engines: { node: '>= 0.4' }

  typed-array-length@1.0.7:
    resolution:
      {
        integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==,
        tarball: https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.7.tgz,
      }
    engines: { node: '>= 0.4' }

  typescript@5.4.5:
    resolution:
      {
        integrity: sha512-vcI4UpRgg81oIRUFwR0WSIHKt11nJ7SAVlYNIu+QpqeyXP+gpQJy/Z4+F0aGxSE4MqwjyXvW/TzgkLAx2AGHwQ==,
        tarball: https://registry.npmjs.org/typescript/-/typescript-5.4.5.tgz,
      }
    engines: { node: '>=14.17' }
    hasBin: true

  ua-parser-js@1.0.40:
    resolution:
      {
        integrity: sha512-z6PJ8Lml+v3ichVojCiB8toQJBuwR42ySM4ezjXIqXK3M0HczmKQ3LF4rhU55PfD99KEEXQG6yb7iOMyvYuHew==,
        tarball: https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-1.0.40.tgz,
      }
    hasBin: true

  unbox-primitive@1.1.0:
    resolution:
      {
        integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==,
        tarball: https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.1.0.tgz,
      }
    engines: { node: '>= 0.4' }

  unc-path-regex@0.1.2:
    resolution:
      {
        integrity: sha512-eXL4nmJT7oCpkZsHZUOJo8hcX3GbsiDOa0Qu9F646fi8dT3XuSVopVqAcEiVzSKKH7UoDti23wNX3qGFxcW5Qg==,
        tarball: https://registry.npmjs.org/unc-path-regex/-/unc-path-regex-0.1.2.tgz,
      }
    engines: { node: '>=0.10.0' }

  undici-types@5.26.5:
    resolution:
      {
        integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==,
        tarball: https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz,
      }

  unicorn-magic@0.1.0:
    resolution:
      {
        integrity: sha512-lRfVq8fE8gz6QMBuDM6a+LO3IAzTi05H6gCVaUpir2E1Rwpo4ZUog45KpNXKC/Mn3Yb9UDuHumeFTo9iV/D9FQ==,
        tarball: https://registry.npmjs.org/unicorn-magic/-/unicorn-magic-0.1.0.tgz,
      }
    engines: { node: '>=18' }

  unixify@1.0.0:
    resolution:
      {
        integrity: sha512-6bc58dPYhCMHHuwxldQxO3RRNZ4eCogZ/st++0+fcC1nr0jiGUtAdBJ2qzmLQWSxbtz42pWt4QQMiZ9HvZf5cg==,
        tarball: https://registry.npmjs.org/unixify/-/unixify-1.0.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  unplugin@1.0.1:
    resolution:
      {
        integrity: sha512-aqrHaVBWW1JVKBHmGo33T5TxeL0qWzfvjWokObHA9bYmN7eNDkwOxmLjhioHl9878qDFMAaT51XNroRyuz7WxA==,
        tarball: https://registry.npmjs.org/unplugin/-/unplugin-1.0.1.tgz,
      }

  update-browserslist-db@1.1.3:
    resolution:
      {
        integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==,
        tarball: https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz,
      }
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  upper-case-first@2.0.2:
    resolution:
      {
        integrity: sha512-514ppYHBaKwfJRK/pNC6c/OxfGa0obSnAl106u97Ed0I625Nin96KAjttZF6ZL3e1XLtphxnqrOi9iWgm+u+bg==,
        tarball: https://registry.npmjs.org/upper-case-first/-/upper-case-first-2.0.2.tgz,
      }

  upper-case@2.0.2:
    resolution:
      {
        integrity: sha512-KgdgDGJt2TpuwBUIjgG6lzw2GWFRCW9Qkfkiv0DxqHHLYJHmtmdUIKcZd8rHgFSjopVTlw6ggzCm1b8MFQwikg==,
        tarball: https://registry.npmjs.org/upper-case/-/upper-case-2.0.2.tgz,
      }

  uri-js@4.4.1:
    resolution:
      {
        integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==,
        tarball: https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz,
      }

  urlpattern-polyfill@10.1.0:
    resolution:
      {
        integrity: sha512-IGjKp/o0NL3Bso1PymYURCJxMPNAf/ILOpendP9f5B6e1rTJgdgiOvgfoT8VxCAdY+Wisb9uhGaJJf3yZ2V9nw==,
        tarball: https://registry.npmjs.org/urlpattern-polyfill/-/urlpattern-polyfill-10.1.0.tgz,
      }

  urlpattern-polyfill@8.0.2:
    resolution:
      {
        integrity: sha512-Qp95D4TPJl1kC9SKigDcqgyM2VDVO4RiJc2d4qe5GrYm+zbIQCWWKAFaJNQ4BhdFeDGwBmAxqJBwWSJDb9T3BQ==,
        tarball: https://registry.npmjs.org/urlpattern-polyfill/-/urlpattern-polyfill-8.0.2.tgz,
      }

  use-callback-ref@1.3.3:
    resolution:
      {
        integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==,
        tarball: https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz,
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sidecar@1.1.3:
    resolution:
      {
        integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==,
        tarball: https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz,
      }
    engines: { node: '>=10' }
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  util-deprecate@1.0.2:
    resolution:
      {
        integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==,
        tarball: https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz,
      }

  uuid@9.0.1:
    resolution:
      {
        integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==,
        tarball: https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz,
      }
    hasBin: true

  vite-plugin-svgr@4.2.0:
    resolution:
      {
        integrity: sha512-SC7+FfVtNQk7So0XMjrrtLAbEC8qjFPifyD7+fs/E6aaNdVde6umlVVh0QuwDLdOMu7vp5RiGFsB70nj5yo0XA==,
        tarball: https://registry.npmjs.org/vite-plugin-svgr/-/vite-plugin-svgr-4.2.0.tgz,
      }
    peerDependencies:
      vite: ^2.6.0 || 3 || 4 || 5

  vite-plugin-webfont-dl@3.10.4:
    resolution:
      {
        integrity: sha512-Odn+9JO0Wz67pSiGGA0IZS7c+m3/KFRFI370WRiP5x20TZFobtaLdIcvfb1pZelRhOIzKndfsoLWrq8144mjzw==,
        tarball: https://registry.npmjs.org/vite-plugin-webfont-dl/-/vite-plugin-webfont-dl-3.10.4.tgz,
      }
    peerDependencies:
      vite: ^2 || ^3 || ^4 || ^5 || ^6

  vite-tsconfig-paths@4.3.2:
    resolution:
      {
        integrity: sha512-0Vd/a6po6Q+86rPlntHye7F31zA2URZMbH8M3saAZ/xR9QoGN/L21bxEGfXdWmFdNkqPpRdxFT7nmNe12e9/uA==,
        tarball: https://registry.npmjs.org/vite-tsconfig-paths/-/vite-tsconfig-paths-4.3.2.tgz,
      }
    peerDependencies:
      vite: '*'
    peerDependenciesMeta:
      vite:
        optional: true

  vite@6.0.11:
    resolution:
      {
        integrity: sha512-4VL9mQPKoHy4+FE0NnRE/kbY51TOfaknxAjt3fJbGJxhIpBZiqVzlZDEesWWsuREXHwNdAoOFZ9MkPEVXczHwg==,
        tarball: https://registry.npmjs.org/vite/-/vite-6.0.11.tgz,
      }
    engines: { node: ^18.0.0 || ^20.0.0 || >=22.0.0 }
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  void-elements@3.1.0:
    resolution:
      {
        integrity: sha512-Dhxzh5HZuiHQhbvTW9AMetFfBHDMYpo23Uo9btPXgdYP+3T5S+p+jgNy7spra+veYhBP2dCSgxR/i2Y02h5/6w==,
        tarball: https://registry.npmjs.org/void-elements/-/void-elements-3.1.0.tgz,
      }
    engines: { node: '>=0.10.0' }

  warning@4.0.3:
    resolution:
      {
        integrity: sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==,
        tarball: https://registry.npmjs.org/warning/-/warning-4.0.3.tgz,
      }

  wcwidth@1.0.1:
    resolution:
      {
        integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==,
        tarball: https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz,
      }

  web-streams-polyfill@3.3.3:
    resolution:
      {
        integrity: sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==,
        tarball: https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz,
      }
    engines: { node: '>= 8' }

  web-vitals@4.2.4:
    resolution:
      {
        integrity: sha512-r4DIlprAGwJ7YM11VZp4R884m0Vmgr6EAKe3P+kO0PPj3Unqyvv59rczf6UiGcb9Z8QxZVcqKNwv/g0WNdWwsw==,
        tarball: https://registry.npmjs.org/web-vitals/-/web-vitals-4.2.4.tgz,
      }

  webcrypto-core@1.8.1:
    resolution:
      {
        integrity: sha512-P+x1MvlNCXlKbLSOY4cYrdreqPG5hbzkmawbcXLKN/mf6DZW0SdNNkZ+sjwsqVkI4A4Ko2sPZmkZtCKY58w83A==,
        tarball: https://registry.npmjs.org/webcrypto-core/-/webcrypto-core-1.8.1.tgz,
      }

  webidl-conversions@3.0.1:
    resolution:
      {
        integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==,
        tarball: https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz,
      }

  webpack-sources@3.3.2:
    resolution:
      {
        integrity: sha512-ykKKus8lqlgXX/1WjudpIEjqsafjOTcOJqxnAbMLAu/KCsDCJ6GBtvscewvTkrn24HsnvFwrSCbenFrhtcCsAA==,
        tarball: https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.3.2.tgz,
      }
    engines: { node: '>=10.13.0' }

  webpack-virtual-modules@0.5.0:
    resolution:
      {
        integrity: sha512-kyDivFZ7ZM0BVOUteVbDFhlRt7Ah/CSPwJdi8hBpkK7QLumUqdLtVfm/PX/hkcnrvr0i77fO5+TjZ94Pe+C9iw==,
        tarball: https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.5.0.tgz,
      }

  whatwg-mimetype@4.0.0:
    resolution:
      {
        integrity: sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg==,
        tarball: https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-4.0.0.tgz,
      }
    engines: { node: '>=18' }

  whatwg-url@5.0.0:
    resolution:
      {
        integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==,
        tarball: https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz,
      }

  which-boxed-primitive@1.1.1:
    resolution:
      {
        integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==,
        tarball: https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz,
      }
    engines: { node: '>= 0.4' }

  which-builtin-type@1.2.1:
    resolution:
      {
        integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==,
        tarball: https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.2.1.tgz,
      }
    engines: { node: '>= 0.4' }

  which-collection@1.0.2:
    resolution:
      {
        integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==,
        tarball: https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz,
      }
    engines: { node: '>= 0.4' }

  which-module@2.0.1:
    resolution:
      {
        integrity: sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==,
        tarball: https://registry.npmjs.org/which-module/-/which-module-2.0.1.tgz,
      }

  which-typed-array@1.1.19:
    resolution:
      {
        integrity: sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==,
        tarball: https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz,
      }
    engines: { node: '>= 0.4' }

  which@2.0.2:
    resolution:
      {
        integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==,
        tarball: https://registry.npmjs.org/which/-/which-2.0.2.tgz,
      }
    engines: { node: '>= 8' }
    hasBin: true

  word-wrap@1.2.5:
    resolution:
      {
        integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==,
        tarball: https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz,
      }
    engines: { node: '>=0.10.0' }

  wrap-ansi@6.2.0:
    resolution:
      {
        integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==,
        tarball: https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz,
      }
    engines: { node: '>=8' }

  wrap-ansi@7.0.0:
    resolution:
      {
        integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==,
        tarball: https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz,
      }
    engines: { node: '>=10' }

  wrap-ansi@8.1.0:
    resolution:
      {
        integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==,
        tarball: https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz,
      }
    engines: { node: '>=12' }

  wrappy@1.0.2:
    resolution:
      {
        integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==,
        tarball: https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz,
      }

  ws@8.18.2:
    resolution:
      {
        integrity: sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==,
        tarball: https://registry.npmjs.org/ws/-/ws-8.18.2.tgz,
      }
    engines: { node: '>=10.0.0' }
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  y18n@4.0.3:
    resolution:
      {
        integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==,
        tarball: https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz,
      }

  y18n@5.0.8:
    resolution:
      {
        integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==,
        tarball: https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz,
      }
    engines: { node: '>=10' }

  yallist@3.1.1:
    resolution:
      {
        integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==,
        tarball: https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz,
      }

  yaml-ast-parser@0.0.43:
    resolution:
      {
        integrity: sha512-2PTINUwsRqSd+s8XxKaJWQlUuEMHJQyEuh2edBbW8KNJz0SJPwUSD2zRWqezFEdN7IzAgeuYHFUCF7o8zRdZ0A==,
        tarball: https://registry.npmjs.org/yaml-ast-parser/-/yaml-ast-parser-0.0.43.tgz,
      }

  yaml@2.8.0:
    resolution:
      {
        integrity: sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==,
        tarball: https://registry.npmjs.org/yaml/-/yaml-2.8.0.tgz,
      }
    engines: { node: '>= 14.6' }
    hasBin: true

  yargs-parser@18.1.3:
    resolution:
      {
        integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==,
        tarball: https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz,
      }
    engines: { node: '>=6' }

  yargs-parser@21.1.1:
    resolution:
      {
        integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==,
        tarball: https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz,
      }
    engines: { node: '>=12' }

  yargs@15.4.1:
    resolution:
      {
        integrity: sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==,
        tarball: https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz,
      }
    engines: { node: '>=8' }

  yargs@17.7.2:
    resolution:
      {
        integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==,
        tarball: https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz,
      }
    engines: { node: '>=12' }

  yocto-queue@0.1.0:
    resolution:
      {
        integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==,
        tarball: https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz,
      }
    engines: { node: '>=10' }

  yocto-queue@1.2.1:
    resolution:
      {
        integrity: sha512-AyeEbWOu/TAXdxlV9wmGcR0+yh2j3vYPGOECcIj2S7MkrLyC7ne+oye2BKTItt0ii2PHk4cDy+95+LshzbXnGg==,
        tarball: https://registry.npmjs.org/yocto-queue/-/yocto-queue-1.2.1.tgz,
      }
    engines: { node: '>=12.20' }

  zen-observable-ts@1.2.5:
    resolution:
      {
        integrity: sha512-QZWQekv6iB72Naeake9hS1KxHlotfRpe+WGNbNx5/ta+R3DNjVO2bswf63gXlWDcs+EMd7XY8HfVQyP1X6T4Zg==,
        tarball: https://registry.npmjs.org/zen-observable-ts/-/zen-observable-ts-1.2.5.tgz,
      }

  zen-observable@0.8.15:
    resolution:
      {
        integrity: sha512-PQ2PC7R9rslx84ndNBZB/Dkv8V8fZEpk83RLgXtYd0fwUgEjseMn1Dgajh2x6S8QbZAFa9p2qVCEuYZNgve0dQ==,
        tarball: https://registry.npmjs.org/zen-observable/-/zen-observable-0.8.15.tgz,
      }

  zod@3.24.1:
    resolution:
      {
        integrity: sha512-muH7gBL9sI1nciMZV67X5fTKKBLtwpZ5VBp1vsOQzj1MhrBZ4wlVCm3gedKZWLp0Oyel8sIGfeiz54Su+OVT+A==,
        tarball: https://registry.npmjs.org/zod/-/zod-3.24.1.tgz,
      }

snapshots:
  '@alloc/quick-lru@5.2.0': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@apollo/client@3.9.9(@types/react@18.2.70)(graphql@16.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@graphql-typed-document-node/core': 3.2.0(graphql@16.8.1)
      '@wry/caches': 1.0.1
      '@wry/equality': 0.5.7
      '@wry/trie': 0.5.0
      graphql: 16.8.1
      graphql-tag: 2.12.6(graphql@16.8.1)
      hoist-non-react-statics: 3.3.2
      optimism: 0.18.1
      prop-types: 15.8.1
      rehackt: 0.0.6(@types/react@18.2.70)(react@18.2.0)
      response-iterator: 0.2.22
      symbol-observable: 4.0.0
      ts-invariant: 0.10.3
      tslib: 2.8.1
      zen-observable-ts: 1.2.5
    optionalDependencies:
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'

  '@ardatan/relay-compiler@12.0.0(graphql@16.8.1)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/runtime': 7.27.6
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
      babel-preset-fbjs: 3.4.0(@babel/core@7.27.4)
      chalk: 4.1.2
      fb-watchman: 2.0.2
      fbjs: 3.0.5
      glob: 7.2.3
      graphql: 16.8.1
      immutable: 3.7.6
      invariant: 2.2.4
      nullthrows: 1.1.1
      relay-runtime: 12.0.0
      signedsource: 1.0.0
      yargs: 15.4.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@ardatan/relay-compiler@12.0.3(graphql@16.8.1)':
    dependencies:
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/runtime': 7.27.6
      chalk: 4.1.2
      fb-watchman: 2.0.2
      graphql: 16.8.1
      immutable: 3.7.6
      invariant: 2.2.4
      nullthrows: 1.1.1
      relay-runtime: 12.0.0
      signedsource: 1.0.0
    transitivePeerDependencies:
      - encoding

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.27.5': {}

  '@babel/core@7.27.4':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.5':
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.27.3':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.27.5
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.0
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/traverse': 7.27.4
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-member-expression-to-functions@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.27.1':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.27.6':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6

  '@babel/parser@7.27.5':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-object-rest-spread@7.20.7(@babel/core@7.27.4)':
    dependencies:
      '@babel/compat-data': 7.27.5
      '@babel/core': 7.27.4
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.27.4)
      '@babel/plugin-transform-parameters': 7.27.1(@babel/core@7.27.4)

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-flow@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-block-scoping@7.27.5(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-classes@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.27.4)
      '@babel/traverse': 7.27.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/template': 7.27.2

  '@babel/plugin-transform-destructuring@7.27.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-flow-strip-types@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-flow': 7.27.1(@babel/core@7.27.4)

  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-parameters@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-display-name@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/runtime@7.27.6': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6

  '@babel/traverse@7.27.4':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.6':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@commitlint/cli@19.2.1(@types/node@20.11.30)(typescript@5.4.5)':
    dependencies:
      '@commitlint/format': 19.8.1
      '@commitlint/lint': 19.8.1
      '@commitlint/load': 19.8.1(@types/node@20.11.30)(typescript@5.4.5)
      '@commitlint/read': 19.8.1
      '@commitlint/types': 19.8.1
      execa: 8.0.1
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/config-conventional@19.1.0':
    dependencies:
      '@commitlint/types': 19.8.1
      conventional-changelog-conventionalcommits: 7.0.2

  '@commitlint/config-validator@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      ajv: 8.17.1

  '@commitlint/ensure@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      lodash.camelcase: 4.3.0
      lodash.kebabcase: 4.1.1
      lodash.snakecase: 4.1.1
      lodash.startcase: 4.4.0
      lodash.upperfirst: 4.3.1

  '@commitlint/execute-rule@19.8.1': {}

  '@commitlint/format@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      chalk: 5.4.1

  '@commitlint/is-ignored@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      semver: 7.7.2

  '@commitlint/lint@19.8.1':
    dependencies:
      '@commitlint/is-ignored': 19.8.1
      '@commitlint/parse': 19.8.1
      '@commitlint/rules': 19.8.1
      '@commitlint/types': 19.8.1

  '@commitlint/load@19.8.1(@types/node@20.11.30)(typescript@5.4.5)':
    dependencies:
      '@commitlint/config-validator': 19.8.1
      '@commitlint/execute-rule': 19.8.1
      '@commitlint/resolve-extends': 19.8.1
      '@commitlint/types': 19.8.1
      chalk: 5.4.1
      cosmiconfig: 9.0.0(typescript@5.4.5)
      cosmiconfig-typescript-loader: 6.1.0(@types/node@20.11.30)(cosmiconfig@9.0.0(typescript@5.4.5))(typescript@5.4.5)
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      lodash.uniq: 4.5.0
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/message@19.8.1': {}

  '@commitlint/parse@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      conventional-changelog-angular: 7.0.0
      conventional-commits-parser: 5.0.0

  '@commitlint/read@19.8.1':
    dependencies:
      '@commitlint/top-level': 19.8.1
      '@commitlint/types': 19.8.1
      git-raw-commits: 4.0.0
      minimist: 1.2.8
      tinyexec: 1.0.1

  '@commitlint/resolve-extends@19.8.1':
    dependencies:
      '@commitlint/config-validator': 19.8.1
      '@commitlint/types': 19.8.1
      global-directory: 4.0.1
      import-meta-resolve: 4.1.0
      lodash.mergewith: 4.6.2
      resolve-from: 5.0.0

  '@commitlint/rules@19.8.1':
    dependencies:
      '@commitlint/ensure': 19.8.1
      '@commitlint/message': 19.8.1
      '@commitlint/to-lines': 19.8.1
      '@commitlint/types': 19.8.1

  '@commitlint/to-lines@19.8.1': {}

  '@commitlint/top-level@19.8.1':
    dependencies:
      find-up: 7.0.0

  '@commitlint/types@19.8.1':
    dependencies:
      '@types/conventional-commits-parser': 5.0.1
      chalk: 5.4.1

  '@envelop/core@5.2.3':
    dependencies:
      '@envelop/instrumentation': 1.0.0
      '@envelop/types': 5.2.1
      '@whatwg-node/promise-helpers': 1.3.2
      tslib: 2.8.1

  '@envelop/instrumentation@1.0.0':
    dependencies:
      '@whatwg-node/promise-helpers': 1.3.2
      tslib: 2.8.1

  '@envelop/types@5.2.1':
    dependencies:
      '@whatwg-node/promise-helpers': 1.3.2
      tslib: 2.8.1

  '@esbuild/aix-ppc64@0.24.2':
    optional: true

  '@esbuild/android-arm64@0.24.2':
    optional: true

  '@esbuild/android-arm@0.24.2':
    optional: true

  '@esbuild/android-x64@0.24.2':
    optional: true

  '@esbuild/darwin-arm64@0.24.2':
    optional: true

  '@esbuild/darwin-x64@0.24.2':
    optional: true

  '@esbuild/freebsd-arm64@0.24.2':
    optional: true

  '@esbuild/freebsd-x64@0.24.2':
    optional: true

  '@esbuild/linux-arm64@0.24.2':
    optional: true

  '@esbuild/linux-arm@0.24.2':
    optional: true

  '@esbuild/linux-ia32@0.24.2':
    optional: true

  '@esbuild/linux-loong64@0.24.2':
    optional: true

  '@esbuild/linux-mips64el@0.24.2':
    optional: true

  '@esbuild/linux-ppc64@0.24.2':
    optional: true

  '@esbuild/linux-riscv64@0.24.2':
    optional: true

  '@esbuild/linux-s390x@0.24.2':
    optional: true

  '@esbuild/linux-x64@0.24.2':
    optional: true

  '@esbuild/netbsd-arm64@0.24.2':
    optional: true

  '@esbuild/netbsd-x64@0.24.2':
    optional: true

  '@esbuild/openbsd-arm64@0.24.2':
    optional: true

  '@esbuild/openbsd-x64@0.24.2':
    optional: true

  '@esbuild/sunos-x64@0.24.2':
    optional: true

  '@esbuild/win32-arm64@0.24.2':
    optional: true

  '@esbuild/win32-ia32@0.24.2':
    optional: true

  '@esbuild/win32-x64@0.24.2':
    optional: true

  '@eslint-community/eslint-utils@4.7.0(eslint@9.16.0(jiti@2.4.2))':
    dependencies:
      eslint: 9.16.0(jiti@2.4.2)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.19.2':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/core@0.13.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/core@0.9.1':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 10.4.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.16.0': {}

  '@eslint/js@9.28.0': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.2.8':
    dependencies:
      '@eslint/core': 0.13.0
      levn: 0.4.1

  '@fastify/busboy@3.1.1': {}

  '@floating-ui/core@1.7.1':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/dom@1.7.1':
    dependencies:
      '@floating-ui/core': 1.7.1
      '@floating-ui/utils': 0.2.9

  '@floating-ui/react-dom@2.1.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@floating-ui/dom': 1.7.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  '@floating-ui/utils@0.2.9': {}

  '@graphql-codegen/add@3.2.3(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 3.1.2(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.4.1

  '@graphql-codegen/add@5.0.3(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.6.3

  '@graphql-codegen/cli@5.0.2(@types/node@20.11.30)(graphql@16.8.1)(typescript@5.4.5)':
    dependencies:
      '@babel/generator': 7.27.5
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6
      '@graphql-codegen/client-preset': 4.8.1(graphql@16.8.1)
      '@graphql-codegen/core': 4.0.2(graphql@16.8.1)
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-tools/apollo-engine-loader': 8.0.20(graphql@16.8.1)
      '@graphql-tools/code-file-loader': 8.1.20(graphql@16.8.1)
      '@graphql-tools/git-loader': 8.0.24(graphql@16.8.1)
      '@graphql-tools/github-loader': 8.0.20(@types/node@20.11.30)(graphql@16.8.1)
      '@graphql-tools/graphql-file-loader': 8.0.20(graphql@16.8.1)
      '@graphql-tools/json-file-loader': 8.0.18(graphql@16.8.1)
      '@graphql-tools/load': 8.1.0(graphql@16.8.1)
      '@graphql-tools/prisma-loader': 8.0.17(@types/node@20.11.30)(graphql@16.8.1)
      '@graphql-tools/url-loader': 8.0.31(@types/node@20.11.30)(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@whatwg-node/fetch': 0.8.8
      chalk: 4.1.2
      cosmiconfig: 8.3.6(typescript@5.4.5)
      debounce: 1.2.1
      detect-indent: 6.1.0
      graphql: 16.8.1
      graphql-config: 5.1.5(@types/node@20.11.30)(graphql@16.8.1)(typescript@5.4.5)
      inquirer: 8.2.6
      is-glob: 4.0.3
      jiti: 1.21.7
      json-to-pretty-yaml: 1.2.2
      listr2: 4.0.5
      log-symbols: 4.1.0
      micromatch: 4.0.8
      shell-quote: 1.8.3
      string-env-interpolation: 1.0.1
      ts-log: 2.2.7
      tslib: 2.8.1
      yaml: 2.8.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@fastify/websocket'
      - '@types/node'
      - bufferutil
      - cosmiconfig-toml-loader
      - crossws
      - encoding
      - enquirer
      - graphql-sock
      - supports-color
      - typescript
      - uWebSockets.js
      - utf-8-validate

  '@graphql-codegen/client-preset@4.8.1(graphql@16.8.1)':
    dependencies:
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/template': 7.27.2
      '@graphql-codegen/add': 5.0.3(graphql@16.8.1)
      '@graphql-codegen/gql-tag-operations': 4.0.17(graphql@16.8.1)
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-codegen/typed-document-node': 5.1.1(graphql@16.8.1)
      '@graphql-codegen/typescript': 4.1.6(graphql@16.8.1)
      '@graphql-codegen/typescript-operations': 4.6.1(graphql@16.8.1)
      '@graphql-codegen/visitor-plugin-common': 5.8.0(graphql@16.8.1)
      '@graphql-tools/documents': 1.0.1(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@graphql-typed-document-node/core': 3.2.0(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding

  '@graphql-codegen/core@4.0.2(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-tools/schema': 10.0.23(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.6.3

  '@graphql-codegen/gql-tag-operations@4.0.17(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-codegen/visitor-plugin-common': 5.8.0(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      auto-bind: 4.0.0
      graphql: 16.8.1
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding

  '@graphql-codegen/near-operation-file-preset@3.0.0(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/add': 3.2.3(graphql@16.8.1)
      '@graphql-codegen/plugin-helpers': 3.1.2(graphql@16.8.1)
      '@graphql-codegen/visitor-plugin-common': 2.13.1(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      parse-filepath: 1.0.2
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@graphql-codegen/plugin-helpers@2.7.2(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 8.13.1(graphql@16.8.1)
      change-case-all: 1.0.14
      common-tags: 1.8.2
      graphql: 16.8.1
      import-from: 4.0.0
      lodash: 4.17.21
      tslib: 2.4.1

  '@graphql-codegen/plugin-helpers@3.1.2(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 9.2.1(graphql@16.8.1)
      change-case-all: 1.0.15
      common-tags: 1.8.2
      graphql: 16.8.1
      import-from: 4.0.0
      lodash: 4.17.21
      tslib: 2.4.1

  '@graphql-codegen/plugin-helpers@5.1.0(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      change-case-all: 1.0.15
      common-tags: 1.8.2
      graphql: 16.8.1
      import-from: 4.0.0
      lodash: 4.17.21
      tslib: 2.6.3

  '@graphql-codegen/schema-ast@4.1.0(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.6.3

  '@graphql-codegen/typed-document-node@5.1.1(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-codegen/visitor-plugin-common': 5.8.0(graphql@16.8.1)
      auto-bind: 4.0.0
      change-case-all: 1.0.15
      graphql: 16.8.1
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding

  '@graphql-codegen/typescript-operations@4.2.0(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-codegen/typescript': 4.1.6(graphql@16.8.1)
      '@graphql-codegen/visitor-plugin-common': 5.1.0(graphql@16.8.1)
      auto-bind: 4.0.0
      graphql: 16.8.1
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding

  '@graphql-codegen/typescript-operations@4.6.1(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-codegen/typescript': 4.1.6(graphql@16.8.1)
      '@graphql-codegen/visitor-plugin-common': 5.8.0(graphql@16.8.1)
      auto-bind: 4.0.0
      graphql: 16.8.1
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding

  '@graphql-codegen/typescript-react-apollo@4.3.0(graphql-tag@2.12.6(graphql@16.8.1))(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 3.1.2(graphql@16.8.1)
      '@graphql-codegen/visitor-plugin-common': 2.13.1(graphql@16.8.1)
      auto-bind: 4.0.0
      change-case-all: 1.0.15
      graphql: 16.8.1
      graphql-tag: 2.12.6(graphql@16.8.1)
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@graphql-codegen/typescript@4.1.6(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-codegen/schema-ast': 4.1.0(graphql@16.8.1)
      '@graphql-codegen/visitor-plugin-common': 5.8.0(graphql@16.8.1)
      auto-bind: 4.0.0
      graphql: 16.8.1
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding

  '@graphql-codegen/visitor-plugin-common@2.13.1(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 2.7.2(graphql@16.8.1)
      '@graphql-tools/optimize': 1.4.0(graphql@16.8.1)
      '@graphql-tools/relay-operation-optimizer': 6.5.18(graphql@16.8.1)
      '@graphql-tools/utils': 8.13.1(graphql@16.8.1)
      auto-bind: 4.0.0
      change-case-all: 1.0.14
      dependency-graph: 0.11.0
      graphql: 16.8.1
      graphql-tag: 2.12.6(graphql@16.8.1)
      parse-filepath: 1.0.2
      tslib: 2.4.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@graphql-codegen/visitor-plugin-common@5.1.0(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-tools/optimize': 2.0.0(graphql@16.8.1)
      '@graphql-tools/relay-operation-optimizer': 7.0.19(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      auto-bind: 4.0.0
      change-case-all: 1.0.15
      dependency-graph: 0.11.0
      graphql: 16.8.1
      graphql-tag: 2.12.6(graphql@16.8.1)
      parse-filepath: 1.0.2
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding

  '@graphql-codegen/visitor-plugin-common@5.8.0(graphql@16.8.1)':
    dependencies:
      '@graphql-codegen/plugin-helpers': 5.1.0(graphql@16.8.1)
      '@graphql-tools/optimize': 2.0.0(graphql@16.8.1)
      '@graphql-tools/relay-operation-optimizer': 7.0.19(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      auto-bind: 4.0.0
      change-case-all: 1.0.15
      dependency-graph: 0.11.0
      graphql: 16.8.1
      graphql-tag: 2.12.6(graphql@16.8.1)
      parse-filepath: 1.0.2
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding

  '@graphql-hive/signal@1.0.0': {}

  '@graphql-tools/apollo-engine-loader@8.0.20(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@whatwg-node/fetch': 0.10.8
      graphql: 16.8.1
      sync-fetch: 0.6.0-2
      tslib: 2.8.1

  '@graphql-tools/batch-execute@9.0.17(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@whatwg-node/promise-helpers': 1.3.2
      dataloader: 2.2.3
      graphql: 16.8.1
      tslib: 2.8.1

  '@graphql-tools/code-file-loader@8.1.20(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/graphql-tag-pluck': 8.3.19(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      globby: 11.1.0
      graphql: 16.8.1
      tslib: 2.8.1
      unixify: 1.0.0
    transitivePeerDependencies:
      - supports-color

  '@graphql-tools/delegate@10.2.19(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/batch-execute': 9.0.17(graphql@16.8.1)
      '@graphql-tools/executor': 1.4.7(graphql@16.8.1)
      '@graphql-tools/schema': 10.0.23(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@repeaterjs/repeater': 3.0.6
      '@whatwg-node/promise-helpers': 1.3.2
      dataloader: 2.2.3
      dset: 3.1.4
      graphql: 16.8.1
      tslib: 2.8.1

  '@graphql-tools/documents@1.0.1(graphql@16.8.1)':
    dependencies:
      graphql: 16.8.1
      lodash.sortby: 4.7.0
      tslib: 2.8.1

  '@graphql-tools/executor-common@0.0.4(graphql@16.8.1)':
    dependencies:
      '@envelop/core': 5.2.3
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1

  '@graphql-tools/executor-graphql-ws@2.0.5(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/executor-common': 0.0.4(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@whatwg-node/disposablestack': 0.0.6
      graphql: 16.8.1
      graphql-ws: 6.0.5(graphql@16.8.1)(ws@8.18.2)
      isomorphic-ws: 5.0.0(ws@8.18.2)
      tslib: 2.8.1
      ws: 8.18.2
    transitivePeerDependencies:
      - '@fastify/websocket'
      - bufferutil
      - crossws
      - uWebSockets.js
      - utf-8-validate

  '@graphql-tools/executor-http@1.3.3(@types/node@20.11.30)(graphql@16.8.1)':
    dependencies:
      '@graphql-hive/signal': 1.0.0
      '@graphql-tools/executor-common': 0.0.4(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@repeaterjs/repeater': 3.0.6
      '@whatwg-node/disposablestack': 0.0.6
      '@whatwg-node/fetch': 0.10.8
      '@whatwg-node/promise-helpers': 1.3.2
      graphql: 16.8.1
      meros: 1.3.0(@types/node@20.11.30)
      tslib: 2.8.1
    transitivePeerDependencies:
      - '@types/node'

  '@graphql-tools/executor-legacy-ws@1.1.17(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@types/ws': 8.18.1
      graphql: 16.8.1
      isomorphic-ws: 5.0.0(ws@8.18.2)
      tslib: 2.8.1
      ws: 8.18.2
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@graphql-tools/executor@1.4.7(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@graphql-typed-document-node/core': 3.2.0(graphql@16.8.1)
      '@repeaterjs/repeater': 3.0.6
      '@whatwg-node/disposablestack': 0.0.6
      '@whatwg-node/promise-helpers': 1.3.2
      graphql: 16.8.1
      tslib: 2.8.1

  '@graphql-tools/git-loader@8.0.24(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/graphql-tag-pluck': 8.3.19(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      is-glob: 4.0.3
      micromatch: 4.0.8
      tslib: 2.8.1
      unixify: 1.0.0
    transitivePeerDependencies:
      - supports-color

  '@graphql-tools/github-loader@8.0.20(@types/node@20.11.30)(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/executor-http': 1.3.3(@types/node@20.11.30)(graphql@16.8.1)
      '@graphql-tools/graphql-tag-pluck': 8.3.19(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@whatwg-node/fetch': 0.10.8
      '@whatwg-node/promise-helpers': 1.3.2
      graphql: 16.8.1
      sync-fetch: 0.6.0-2
      tslib: 2.8.1
    transitivePeerDependencies:
      - '@types/node'
      - supports-color

  '@graphql-tools/graphql-file-loader@8.0.20(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/import': 7.0.19(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      globby: 11.1.0
      graphql: 16.8.1
      tslib: 2.8.1
      unixify: 1.0.0

  '@graphql-tools/graphql-tag-pluck@8.3.19(graphql@16.8.1)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/parser': 7.27.5
      '@babel/plugin-syntax-import-assertions': 7.27.1(@babel/core@7.27.4)
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@graphql-tools/import@7.0.19(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      resolve-from: 5.0.0
      tslib: 2.8.1

  '@graphql-tools/json-file-loader@8.0.18(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      globby: 11.1.0
      graphql: 16.8.1
      tslib: 2.8.1
      unixify: 1.0.0

  '@graphql-tools/load@8.1.0(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/schema': 10.0.23(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      p-limit: 3.1.0
      tslib: 2.8.1

  '@graphql-tools/merge@9.0.24(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.8.1

  '@graphql-tools/optimize@1.4.0(graphql@16.8.1)':
    dependencies:
      graphql: 16.8.1
      tslib: 2.6.3

  '@graphql-tools/optimize@2.0.0(graphql@16.8.1)':
    dependencies:
      graphql: 16.8.1
      tslib: 2.6.3

  '@graphql-tools/prisma-loader@8.0.17(@types/node@20.11.30)(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/url-loader': 8.0.31(@types/node@20.11.30)(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@types/js-yaml': 4.0.9
      '@whatwg-node/fetch': 0.10.8
      chalk: 4.1.2
      debug: 4.4.1
      dotenv: 16.5.0
      graphql: 16.8.1
      graphql-request: 6.1.0(graphql@16.8.1)
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      jose: 5.10.0
      js-yaml: 4.1.0
      lodash: 4.17.21
      scuid: 1.1.0
      tslib: 2.8.1
      yaml-ast-parser: 0.0.43
    transitivePeerDependencies:
      - '@fastify/websocket'
      - '@types/node'
      - bufferutil
      - crossws
      - encoding
      - supports-color
      - uWebSockets.js
      - utf-8-validate

  '@graphql-tools/relay-operation-optimizer@6.5.18(graphql@16.8.1)':
    dependencies:
      '@ardatan/relay-compiler': 12.0.0(graphql@16.8.1)
      '@graphql-tools/utils': 9.2.1(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@graphql-tools/relay-operation-optimizer@7.0.19(graphql@16.8.1)':
    dependencies:
      '@ardatan/relay-compiler': 12.0.3(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.6.3
    transitivePeerDependencies:
      - encoding

  '@graphql-tools/schema@10.0.23(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/merge': 9.0.24(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.8.1

  '@graphql-tools/url-loader@8.0.31(@types/node@20.11.30)(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/executor-graphql-ws': 2.0.5(graphql@16.8.1)
      '@graphql-tools/executor-http': 1.3.3(@types/node@20.11.30)(graphql@16.8.1)
      '@graphql-tools/executor-legacy-ws': 1.1.17(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@graphql-tools/wrap': 10.1.0(graphql@16.8.1)
      '@types/ws': 8.18.1
      '@whatwg-node/fetch': 0.10.8
      '@whatwg-node/promise-helpers': 1.3.2
      graphql: 16.8.1
      isomorphic-ws: 5.0.0(ws@8.18.2)
      sync-fetch: 0.6.0-2
      tslib: 2.8.1
      ws: 8.18.2
    transitivePeerDependencies:
      - '@fastify/websocket'
      - '@types/node'
      - bufferutil
      - crossws
      - uWebSockets.js
      - utf-8-validate

  '@graphql-tools/utils@10.8.6(graphql@16.8.1)':
    dependencies:
      '@graphql-typed-document-node/core': 3.2.0(graphql@16.8.1)
      '@whatwg-node/promise-helpers': 1.3.2
      cross-inspect: 1.0.1
      dset: 3.1.4
      graphql: 16.8.1
      tslib: 2.8.1

  '@graphql-tools/utils@8.13.1(graphql@16.8.1)':
    dependencies:
      graphql: 16.8.1
      tslib: 2.6.3

  '@graphql-tools/utils@9.2.1(graphql@16.8.1)':
    dependencies:
      '@graphql-typed-document-node/core': 3.2.0(graphql@16.8.1)
      graphql: 16.8.1
      tslib: 2.6.3

  '@graphql-tools/wrap@10.1.0(graphql@16.8.1)':
    dependencies:
      '@graphql-tools/delegate': 10.2.19(graphql@16.8.1)
      '@graphql-tools/schema': 10.0.23(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      '@whatwg-node/promise-helpers': 1.3.2
      graphql: 16.8.1
      tslib: 2.8.1

  '@graphql-typed-document-node/core@3.2.0(graphql@16.8.1)':
    dependencies:
      graphql: 16.8.1

  '@hookform/resolvers@3.10.0(react-hook-form@7.51.1(react@18.2.0))':
    dependencies:
      react-hook-form: 7.51.1(react@18.2.0)

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.3': {}

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@jest/expect-utils@29.7.0':
    dependencies:
      jest-get-type: 29.6.3

  '@jest/schemas@29.6.3':
    dependencies:
      '@sinclair/typebox': 0.27.8

  '@jest/types@29.6.3':
    dependencies:
      '@jest/schemas': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 20.11.30
      '@types/yargs': 17.0.33
      chalk: 4.1.2

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@keyv/serialize@1.0.3':
    dependencies:
      buffer: 6.0.3

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@peculiar/asn1-schema@2.3.15':
    dependencies:
      asn1js: 3.0.6
      pvtsutils: 1.3.6
      tslib: 2.8.1

  '@peculiar/json-schema@1.1.12':
    dependencies:
      tslib: 2.8.1

  '@peculiar/webcrypto@1.5.0':
    dependencies:
      '@peculiar/asn1-schema': 2.3.15
      '@peculiar/json-schema': 1.1.12
      pvtsutils: 1.3.6
      tslib: 2.8.1
      webcrypto-core: 1.8.1

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@pkgr/core@0.2.7': {}

  '@popperjs/core@2.11.8': {}

  '@radix-ui/number@1.1.0': {}

  '@radix-ui/primitive@1.1.1': {}

  '@radix-ui/react-accordion@1.2.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collapsible': 1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-collection': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-arrow@1.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-arrow@1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-checkbox@1.1.4(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-presence': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-previous': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-size': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-collapsible@1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-presence': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-collection@1.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-slot': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-collection@1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-slot': 1.1.2(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-compose-refs@1.1.1(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-context@1.1.1(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-dialog@1.1.4(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-portal': 1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-presence': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-slot': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      aria-hidden: 1.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.7.1(@types/react@18.2.70)(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-direction@1.1.0(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-dismissable-layer@1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-escape-keydown': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-dismissable-layer@1.1.5(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-escape-keydown': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-dropdown-menu@2.1.6(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-menu': 2.1.6(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-focus-guards@1.1.1(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-focus-scope@1.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-focus-scope@1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-id@1.1.0(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-label@2.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-menu@2.1.6(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collection': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.1.5(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-popper': 1.2.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-portal': 1.1.4(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-presence': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-roving-focus': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-slot': 1.1.2(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      aria-hidden: 1.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.7.1(@types/react@18.2.70)(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-popover@1.1.4(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-popper': 1.2.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-portal': 1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-presence': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-slot': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      aria-hidden: 1.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.7.1(@types/react@18.2.70)(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-popper@1.2.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@floating-ui/react-dom': 2.1.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-arrow': 1.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-rect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-size': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/rect': 1.1.0
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-popper@1.2.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@floating-ui/react-dom': 2.1.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-arrow': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-rect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-size': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/rect': 1.1.0
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-portal@1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-portal@1.1.4(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-presence@1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-primitive@2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-slot': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-primitive@2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-slot': 1.1.2(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-radio-group@1.2.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-presence': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-roving-focus': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-previous': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-size': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-roving-focus@1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collection': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-select@2.1.6(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/number': 1.1.0
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collection': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.1.5(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-popper': 1.2.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-portal': 1.1.4(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-slot': 1.1.2(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-previous': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-visually-hidden': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      aria-hidden: 1.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.7.1(@types/react@18.2.70)(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-separator@1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-slider@1.2.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/number': 1.1.0
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-collection': 1.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-previous': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-size': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-slot@1.1.1(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-slot@1.1.2(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-switch@1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-previous': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-size': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-tooltip@1.1.6(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.1
      '@radix-ui/react-compose-refs': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-popper': 1.2.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-portal': 1.1.3(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-presence': 1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@radix-ui/react-slot': 1.1.1(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      '@radix-ui/react-visually-hidden': 1.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-use-previous@1.1.0(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-use-rect@1.1.0(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      '@radix-ui/rect': 1.1.0
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-use-size@1.1.0(@types/react@18.2.70)(react@18.2.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.70)(react@18.2.0)
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.70

  '@radix-ui/react-visually-hidden@1.1.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.1(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/react-visually-hidden@1.1.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.2(@types/react-dom@18.2.22)(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70
      '@types/react-dom': 18.2.22

  '@radix-ui/rect@1.1.0': {}

  '@react-spring/types@9.7.3': {}

  '@remix-run/router@1.15.3': {}

  '@repeaterjs/repeater@3.0.6': {}

  '@rollup/pluginutils@5.1.4(rollup@4.41.1)':
    dependencies:
      '@types/estree': 1.0.7
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.41.1

  '@rollup/rollup-android-arm-eabi@4.41.1':
    optional: true

  '@rollup/rollup-android-arm64@4.41.1':
    optional: true

  '@rollup/rollup-darwin-arm64@4.41.1':
    optional: true

  '@rollup/rollup-darwin-x64@4.41.1':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.41.1':
    optional: true

  '@rollup/rollup-freebsd-x64@4.41.1':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.41.1':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.41.1':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.41.1':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.41.1':
    optional: true

  '@rudderstack/analytics-js@3.7.11': {}

  '@savvywombat/tailwindcss-grid-areas@4.0.0(tailwindcss@3.4.17)':
    dependencies:
      tailwindcss: 3.4.17

  '@sentry-internal/feedback@7.108.0':
    dependencies:
      '@sentry/core': 7.108.0
      '@sentry/types': 7.108.0
      '@sentry/utils': 7.108.0

  '@sentry-internal/replay-canvas@7.108.0':
    dependencies:
      '@sentry/core': 7.108.0
      '@sentry/replay': 7.108.0
      '@sentry/types': 7.108.0
      '@sentry/utils': 7.108.0

  '@sentry-internal/tracing@7.108.0':
    dependencies:
      '@sentry/core': 7.108.0
      '@sentry/types': 7.108.0
      '@sentry/utils': 7.108.0

  '@sentry/babel-plugin-component-annotate@2.16.1': {}

  '@sentry/browser@7.108.0':
    dependencies:
      '@sentry-internal/feedback': 7.108.0
      '@sentry-internal/replay-canvas': 7.108.0
      '@sentry-internal/tracing': 7.108.0
      '@sentry/core': 7.108.0
      '@sentry/replay': 7.108.0
      '@sentry/types': 7.108.0
      '@sentry/utils': 7.108.0

  '@sentry/bundler-plugin-core@2.16.1':
    dependencies:
      '@babel/core': 7.27.4
      '@sentry/babel-plugin-component-annotate': 2.16.1
      '@sentry/cli': 2.30.2
      dotenv: 16.5.0
      find-up: 5.0.0
      glob: 9.3.5
      magic-string: 0.30.8
      unplugin: 1.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sentry/cli-darwin@2.30.2':
    optional: true

  '@sentry/cli-linux-arm64@2.30.2':
    optional: true

  '@sentry/cli-linux-arm@2.30.2':
    optional: true

  '@sentry/cli-linux-i686@2.30.2':
    optional: true

  '@sentry/cli-linux-x64@2.30.2':
    optional: true

  '@sentry/cli-win32-i686@2.30.2':
    optional: true

  '@sentry/cli-win32-x64@2.30.2':
    optional: true

  '@sentry/cli@2.30.2':
    dependencies:
      https-proxy-agent: 5.0.1
      node-fetch: 2.7.0
      progress: 2.0.3
      proxy-from-env: 1.1.0
      which: 2.0.2
    optionalDependencies:
      '@sentry/cli-darwin': 2.30.2
      '@sentry/cli-linux-arm': 2.30.2
      '@sentry/cli-linux-arm64': 2.30.2
      '@sentry/cli-linux-i686': 2.30.2
      '@sentry/cli-linux-x64': 2.30.2
      '@sentry/cli-win32-i686': 2.30.2
      '@sentry/cli-win32-x64': 2.30.2
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sentry/core@7.108.0':
    dependencies:
      '@sentry/types': 7.108.0
      '@sentry/utils': 7.108.0

  '@sentry/react@7.108.0(react@18.2.0)':
    dependencies:
      '@sentry/browser': 7.108.0
      '@sentry/core': 7.108.0
      '@sentry/types': 7.108.0
      '@sentry/utils': 7.108.0
      hoist-non-react-statics: 3.3.2
      react: 18.2.0

  '@sentry/replay@7.108.0':
    dependencies:
      '@sentry-internal/tracing': 7.108.0
      '@sentry/core': 7.108.0
      '@sentry/types': 7.108.0
      '@sentry/utils': 7.108.0

  '@sentry/types@7.108.0': {}

  '@sentry/utils@7.108.0':
    dependencies:
      '@sentry/types': 7.108.0

  '@sentry/vite-plugin@2.16.1':
    dependencies:
      '@sentry/bundler-plugin-core': 2.16.1
      unplugin: 1.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sinclair/typebox@0.27.8': {}

  '@svgr/babel-plugin-add-jsx-attribute@8.0.0(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4

  '@svgr/babel-plugin-remove-jsx-attribute@8.0.0(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4

  '@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4

  '@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4

  '@svgr/babel-plugin-svg-dynamic-title@8.0.0(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4

  '@svgr/babel-plugin-svg-em-dimensions@8.0.0(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4

  '@svgr/babel-plugin-transform-react-native-svg@8.1.0(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4

  '@svgr/babel-plugin-transform-svg-component@8.0.0(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4

  '@svgr/babel-preset@8.1.0(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@svgr/babel-plugin-add-jsx-attribute': 8.0.0(@babel/core@7.27.4)
      '@svgr/babel-plugin-remove-jsx-attribute': 8.0.0(@babel/core@7.27.4)
      '@svgr/babel-plugin-remove-jsx-empty-expression': 8.0.0(@babel/core@7.27.4)
      '@svgr/babel-plugin-replace-jsx-attribute-value': 8.0.0(@babel/core@7.27.4)
      '@svgr/babel-plugin-svg-dynamic-title': 8.0.0(@babel/core@7.27.4)
      '@svgr/babel-plugin-svg-em-dimensions': 8.0.0(@babel/core@7.27.4)
      '@svgr/babel-plugin-transform-react-native-svg': 8.1.0(@babel/core@7.27.4)
      '@svgr/babel-plugin-transform-svg-component': 8.0.0(@babel/core@7.27.4)

  '@svgr/core@8.1.0(typescript@5.4.5)':
    dependencies:
      '@babel/core': 7.27.4
      '@svgr/babel-preset': 8.1.0(@babel/core@7.27.4)
      camelcase: 6.3.0
      cosmiconfig: 8.3.6(typescript@5.4.5)
      snake-case: 3.0.4
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@svgr/hast-util-to-babel-ast@8.0.0':
    dependencies:
      '@babel/types': 7.27.6
      entities: 4.5.0

  '@svgr/plugin-jsx@8.1.0(@svgr/core@8.1.0(typescript@5.4.5))':
    dependencies:
      '@babel/core': 7.27.4
      '@svgr/babel-preset': 8.1.0(@babel/core@7.27.4)
      '@svgr/core': 8.1.0(typescript@5.4.5)
      '@svgr/hast-util-to-babel-ast': 8.0.0
      svg-parser: 2.0.4
    transitivePeerDependencies:
      - supports-color

  '@swc/core-darwin-arm64@1.11.31':
    optional: true

  '@swc/core-darwin-x64@1.11.31':
    optional: true

  '@swc/core-linux-arm-gnueabihf@1.11.31':
    optional: true

  '@swc/core-linux-arm64-gnu@1.11.31':
    optional: true

  '@swc/core-linux-arm64-musl@1.11.31':
    optional: true

  '@swc/core-linux-x64-gnu@1.11.31':
    optional: true

  '@swc/core-linux-x64-musl@1.11.31':
    optional: true

  '@swc/core-win32-arm64-msvc@1.11.31':
    optional: true

  '@swc/core-win32-ia32-msvc@1.11.31':
    optional: true

  '@swc/core-win32-x64-msvc@1.11.31':
    optional: true

  '@swc/core@1.11.31':
    dependencies:
      '@swc/counter': 0.1.3
      '@swc/types': 0.1.21
    optionalDependencies:
      '@swc/core-darwin-arm64': 1.11.31
      '@swc/core-darwin-x64': 1.11.31
      '@swc/core-linux-arm-gnueabihf': 1.11.31
      '@swc/core-linux-arm64-gnu': 1.11.31
      '@swc/core-linux-arm64-musl': 1.11.31
      '@swc/core-linux-x64-gnu': 1.11.31
      '@swc/core-linux-x64-musl': 1.11.31
      '@swc/core-win32-arm64-msvc': 1.11.31
      '@swc/core-win32-ia32-msvc': 1.11.31
      '@swc/core-win32-x64-msvc': 1.11.31

  '@swc/counter@0.1.3': {}

  '@swc/types@0.1.21':
    dependencies:
      '@swc/counter': 0.1.3

  '@tanstack/eslint-plugin-query@5.78.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)':
    dependencies:
      '@typescript-eslint/utils': 8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)
      eslint: 9.16.0(jiti@2.4.2)
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@types/apollo-upload-client@18.0.0(@types/react@18.2.70)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@apollo/client': 3.9.9(@types/react@18.2.70)(graphql@16.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@types/extract-files': 13.0.1
      graphql: 16.8.1
    transitivePeerDependencies:
      - '@types/react'
      - graphql-ws
      - react
      - react-dom
      - subscriptions-transport-ws

  '@types/conventional-commits-parser@5.0.1':
    dependencies:
      '@types/node': 20.11.30

  '@types/estree@1.0.7': {}

  '@types/extract-files@13.0.1': {}

  '@types/istanbul-lib-coverage@2.0.6': {}

  '@types/istanbul-lib-report@3.0.3':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6

  '@types/istanbul-reports@3.0.4':
    dependencies:
      '@types/istanbul-lib-report': 3.0.3

  '@types/jest@29.5.12':
    dependencies:
      expect: 29.7.0
      pretty-format: 29.7.0

  '@types/js-cookie@2.2.7': {}

  '@types/js-yaml@4.0.9': {}

  '@types/json-schema@7.0.15': {}

  '@types/lodash@4.17.15': {}

  '@types/node@20.11.30':
    dependencies:
      undici-types: 5.26.5

  '@types/prop-types@15.7.14': {}

  '@types/react-dom@18.2.22':
    dependencies:
      '@types/react': 18.2.70

  '@types/react@18.2.70':
    dependencies:
      '@types/prop-types': 15.7.14
      '@types/scheduler': 0.26.0
      csstype: 3.1.3

  '@types/scheduler@0.26.0': {}

  '@types/stack-utils@2.0.3': {}

  '@types/uuid@9.0.8': {}

  '@types/ws@8.18.1':
    dependencies:
      '@types/node': 20.11.30

  '@types/yargs-parser@21.0.3': {}

  '@types/yargs@17.0.33':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@typescript-eslint/eslint-plugin@8.34.0(@typescript-eslint/parser@8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5))(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)
      '@typescript-eslint/scope-manager': 8.34.0
      '@typescript-eslint/type-utils': 8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)
      '@typescript-eslint/utils': 8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)
      '@typescript-eslint/visitor-keys': 8.34.0
      eslint: 9.16.0(jiti@2.4.2)
      graphemer: 1.4.0
      ignore: 7.0.5
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.4.5)
      typescript: 5.4.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.34.0
      '@typescript-eslint/types': 8.34.0
      '@typescript-eslint/typescript-estree': 8.34.0(typescript@5.4.5)
      '@typescript-eslint/visitor-keys': 8.34.0
      debug: 4.4.1
      eslint: 9.16.0(jiti@2.4.2)
      typescript: 5.4.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/project-service@8.34.0(typescript@5.4.5)':
    dependencies:
      '@typescript-eslint/tsconfig-utils': 8.34.0(typescript@5.4.5)
      '@typescript-eslint/types': 8.34.0
      debug: 4.4.1
      typescript: 5.4.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.34.0':
    dependencies:
      '@typescript-eslint/types': 8.34.0
      '@typescript-eslint/visitor-keys': 8.34.0

  '@typescript-eslint/tsconfig-utils@8.34.0(typescript@5.4.5)':
    dependencies:
      typescript: 5.4.5

  '@typescript-eslint/type-utils@8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.34.0(typescript@5.4.5)
      '@typescript-eslint/utils': 8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)
      debug: 4.4.1
      eslint: 9.16.0(jiti@2.4.2)
      ts-api-utils: 2.1.0(typescript@5.4.5)
      typescript: 5.4.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.34.0': {}

  '@typescript-eslint/typescript-estree@8.34.0(typescript@5.4.5)':
    dependencies:
      '@typescript-eslint/project-service': 8.34.0(typescript@5.4.5)
      '@typescript-eslint/tsconfig-utils': 8.34.0(typescript@5.4.5)
      '@typescript-eslint/types': 8.34.0
      '@typescript-eslint/visitor-keys': 8.34.0
      debug: 4.4.1
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0(typescript@5.4.5)
      typescript: 5.4.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.34.0(eslint@9.16.0(jiti@2.4.2))(typescript@5.4.5)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.16.0(jiti@2.4.2))
      '@typescript-eslint/scope-manager': 8.34.0
      '@typescript-eslint/types': 8.34.0
      '@typescript-eslint/typescript-estree': 8.34.0(typescript@5.4.5)
      eslint: 9.16.0(jiti@2.4.2)
      typescript: 5.4.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.34.0':
    dependencies:
      '@typescript-eslint/types': 8.34.0
      eslint-visitor-keys: 4.2.1

  '@vitejs/plugin-react-swc@3.7.2(vite@6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0))':
    dependencies:
      '@swc/core': 1.11.31
      vite: 6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0)
    transitivePeerDependencies:
      - '@swc/helpers'

  '@whatwg-node/disposablestack@0.0.6':
    dependencies:
      '@whatwg-node/promise-helpers': 1.3.2
      tslib: 2.8.1

  '@whatwg-node/events@0.0.3': {}

  '@whatwg-node/fetch@0.10.8':
    dependencies:
      '@whatwg-node/node-fetch': 0.7.21
      urlpattern-polyfill: 10.1.0

  '@whatwg-node/fetch@0.8.8':
    dependencies:
      '@peculiar/webcrypto': 1.5.0
      '@whatwg-node/node-fetch': 0.3.6
      busboy: 1.6.0
      urlpattern-polyfill: 8.0.2
      web-streams-polyfill: 3.3.3

  '@whatwg-node/node-fetch@0.3.6':
    dependencies:
      '@whatwg-node/events': 0.0.3
      busboy: 1.6.0
      fast-querystring: 1.1.2
      fast-url-parser: 1.1.3
      tslib: 2.8.1

  '@whatwg-node/node-fetch@0.7.21':
    dependencies:
      '@fastify/busboy': 3.1.1
      '@whatwg-node/disposablestack': 0.0.6
      '@whatwg-node/promise-helpers': 1.3.2
      tslib: 2.8.1

  '@whatwg-node/promise-helpers@1.3.2':
    dependencies:
      tslib: 2.8.1

  '@wry/caches@1.0.1':
    dependencies:
      tslib: 2.8.1

  '@wry/context@0.7.4':
    dependencies:
      tslib: 2.8.1

  '@wry/equality@0.5.7':
    dependencies:
      tslib: 2.8.1

  '@wry/trie@0.5.0':
    dependencies:
      tslib: 2.8.1

  '@xobotyi/scrollbar-width@1.9.5': {}

  JSONStream@1.3.5:
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn@8.14.1: {}

  acorn@8.15.0: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  agent-base@7.1.3: {}

  aggregate-error@3.1.0:
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  apollo-upload-client@18.0.1(@apollo/client@3.9.9(@types/react@18.2.70)(graphql@16.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(graphql@16.8.1):
    dependencies:
      '@apollo/client': 3.9.9(@types/react@18.2.70)(graphql@16.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      extract-files: 13.0.0
      graphql: 16.8.1

  arg@5.0.2: {}

  argparse@2.0.1: {}

  aria-hidden@1.2.6:
    dependencies:
      tslib: 2.8.1

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5

  array-ify@1.0.0: {}

  array-includes@3.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1
      math-intrinsics: 1.1.0

  array-union@2.1.0: {}

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  asap@2.0.6: {}

  asn1js@3.0.6:
    dependencies:
      pvtsutils: 1.3.6
      pvutils: 1.1.3
      tslib: 2.8.1

  astral-regex@2.0.0: {}

  async-function@1.0.0: {}

  asynckit@0.4.0: {}

  auto-bind@4.0.0: {}

  autoprefixer@10.4.20(postcss@8.5.0):
    dependencies:
      browserslist: 4.25.0
      caniuse-lite: 1.0.30001721
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.0
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  axios@1.9.0:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-plugin-syntax-trailing-function-commas@7.0.0-beta.0: {}

  babel-preset-fbjs@3.4.0(@babel/core@7.27.4):
    dependencies:
      '@babel/core': 7.27.4
      '@babel/plugin-proposal-class-properties': 7.18.6(@babel/core@7.27.4)
      '@babel/plugin-proposal-object-rest-spread': 7.20.7(@babel/core@7.27.4)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.27.4)
      '@babel/plugin-syntax-flow': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.27.4)
      '@babel/plugin-transform-arrow-functions': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-block-scoped-functions': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-block-scoping': 7.27.5(@babel/core@7.27.4)
      '@babel/plugin-transform-classes': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-computed-properties': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-destructuring': 7.27.3(@babel/core@7.27.4)
      '@babel/plugin-transform-flow-strip-types': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-for-of': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-function-name': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-literals': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-member-expression-literals': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-modules-commonjs': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-object-super': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-parameters': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-property-literals': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-react-display-name': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-shorthand-properties': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-spread': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-template-literals': 7.27.1(@babel/core@7.27.4)
      babel-plugin-syntax-trailing-function-commas: 7.0.0-beta.0
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  binary-extensions@2.3.0: {}

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.25.0:
    dependencies:
      caniuse-lite: 1.0.30001721
      electron-to-chromium: 1.5.165
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.0)

  bser@2.1.1:
    dependencies:
      node-int64: 0.4.0

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  busboy@1.6.0:
    dependencies:
      streamsearch: 1.1.0

  cacheable@1.9.0:
    dependencies:
      hookified: 1.9.1
      keyv: 5.3.3

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  camel-case@4.1.2:
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.6.3

  camelcase-css@2.0.1: {}

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  caniuse-lite@1.0.30001721: {}

  capital-case@1.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3
      upper-case-first: 2.0.2

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.4.1: {}

  change-case-all@1.0.14:
    dependencies:
      change-case: 4.1.2
      is-lower-case: 2.0.2
      is-upper-case: 2.0.2
      lower-case: 2.0.2
      lower-case-first: 2.0.2
      sponge-case: 1.0.1
      swap-case: 2.0.2
      title-case: 3.0.3
      upper-case: 2.0.2
      upper-case-first: 2.0.2

  change-case-all@1.0.15:
    dependencies:
      change-case: 4.1.2
      is-lower-case: 2.0.2
      is-upper-case: 2.0.2
      lower-case: 2.0.2
      lower-case-first: 2.0.2
      sponge-case: 1.0.1
      swap-case: 2.0.2
      title-case: 3.0.3
      upper-case: 2.0.2
      upper-case-first: 2.0.2

  change-case@4.1.2:
    dependencies:
      camel-case: 4.1.2
      capital-case: 1.0.4
      constant-case: 3.0.4
      dot-case: 3.0.4
      header-case: 2.0.4
      no-case: 3.0.4
      param-case: 3.0.4
      pascal-case: 3.1.2
      path-case: 3.0.4
      sentence-case: 3.0.4
      snake-case: 3.0.4
      tslib: 2.6.3

  chardet@0.7.0: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  ci-info@3.9.0: {}

  class-variance-authority@0.7.1:
    dependencies:
      clsx: 2.1.1

  clean-css@5.3.3:
    dependencies:
      source-map: 0.6.1

  clean-stack@2.2.0: {}

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-spinners@2.9.2: {}

  cli-truncate@2.1.0:
    dependencies:
      slice-ansi: 3.0.0
      string-width: 4.2.3

  cli-width@3.0.0: {}

  cliui@6.0.0:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone@1.0.4: {}

  clsx@2.1.1: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@4.1.1: {}

  common-tags@1.8.2: {}

  compare-func@2.0.0:
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0

  concat-map@0.0.1: {}

  constant-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3
      upper-case: 2.0.2

  conventional-changelog-angular@7.0.0:
    dependencies:
      compare-func: 2.0.0

  conventional-changelog-conventionalcommits@7.0.2:
    dependencies:
      compare-func: 2.0.0

  conventional-commits-parser@5.0.0:
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 2.0.0
      meow: 12.1.1
      split2: 4.2.0

  convert-source-map@2.0.0: {}

  copy-to-clipboard@3.3.3:
    dependencies:
      toggle-selection: 1.0.6

  core-js@3.43.0: {}

  cosmiconfig-typescript-loader@6.1.0(@types/node@20.11.30)(cosmiconfig@9.0.0(typescript@5.4.5))(typescript@5.4.5):
    dependencies:
      '@types/node': 20.11.30
      cosmiconfig: 9.0.0(typescript@5.4.5)
      jiti: 2.4.2
      typescript: 5.4.5

  cosmiconfig@8.3.6(typescript@5.4.5):
    dependencies:
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
      path-type: 4.0.0
    optionalDependencies:
      typescript: 5.4.5

  cosmiconfig@9.0.0(typescript@5.4.5):
    dependencies:
      env-paths: 2.2.1
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
    optionalDependencies:
      typescript: 5.4.5

  cross-fetch@3.2.0:
    dependencies:
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding

  cross-fetch@4.0.0:
    dependencies:
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding

  cross-inspect@1.0.1:
    dependencies:
      tslib: 2.8.1

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-in-js-utils@3.1.0:
    dependencies:
      hyphenate-style-name: 1.1.0

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  cssesc@3.0.0: {}

  csstype@3.1.3: {}

  dargs@8.1.0: {}

  data-uri-to-buffer@4.0.1: {}

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  dataloader@2.2.3: {}

  date-fns@3.6.0: {}

  debounce@1.2.1: {}

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  decamelize@1.2.0: {}

  deep-is@0.1.4: {}

  defaults@1.0.4:
    dependencies:
      clone: 1.0.4

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  dependency-graph@0.11.0: {}

  detect-indent@6.1.0: {}

  detect-node-es@1.1.0: {}

  didyoumean@1.2.2: {}

  diff-sequences@29.6.3: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dlv@1.1.3: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3

  dot-prop@5.3.0:
    dependencies:
      is-obj: 2.0.0

  dotenv@16.5.0: {}

  dset@3.1.4: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  eastasianwidth@0.2.0: {}

  electron-to-chromium@1.5.165: {}

  embla-carousel-autoplay@8.5.2(embla-carousel@8.5.2):
    dependencies:
      embla-carousel: 8.5.2

  embla-carousel-fade@8.5.2(embla-carousel@8.5.2):
    dependencies:
      embla-carousel: 8.5.2

  embla-carousel-react@8.5.2(react@18.2.0):
    dependencies:
      embla-carousel: 8.5.2
      embla-carousel-reactive-utils: 8.5.2(embla-carousel@8.5.2)
      react: 18.2.0

  embla-carousel-reactive-utils@8.5.2(embla-carousel@8.5.2):
    dependencies:
      embla-carousel: 8.5.2

  embla-carousel@8.5.2: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  entities@4.5.0: {}

  env-cmd@10.1.0:
    dependencies:
      commander: 4.1.1
      cross-spawn: 7.0.6

  env-paths@2.2.1: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  es-abstract@1.24.0:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-negative-zero: 2.0.3
      is-regex: 1.2.1
      is-set: 2.0.3
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      stop-iteration-iterator: 1.1.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.1.0:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  esbuild@0.24.2:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.24.2
      '@esbuild/android-arm': 0.24.2
      '@esbuild/android-arm64': 0.24.2
      '@esbuild/android-x64': 0.24.2
      '@esbuild/darwin-arm64': 0.24.2
      '@esbuild/darwin-x64': 0.24.2
      '@esbuild/freebsd-arm64': 0.24.2
      '@esbuild/freebsd-x64': 0.24.2
      '@esbuild/linux-arm': 0.24.2
      '@esbuild/linux-arm64': 0.24.2
      '@esbuild/linux-ia32': 0.24.2
      '@esbuild/linux-loong64': 0.24.2
      '@esbuild/linux-mips64el': 0.24.2
      '@esbuild/linux-ppc64': 0.24.2
      '@esbuild/linux-riscv64': 0.24.2
      '@esbuild/linux-s390x': 0.24.2
      '@esbuild/linux-x64': 0.24.2
      '@esbuild/netbsd-arm64': 0.24.2
      '@esbuild/netbsd-x64': 0.24.2
      '@esbuild/openbsd-arm64': 0.24.2
      '@esbuild/openbsd-x64': 0.24.2
      '@esbuild/sunos-x64': 0.24.2
      '@esbuild/win32-arm64': 0.24.2
      '@esbuild/win32-ia32': 0.24.2
      '@esbuild/win32-x64': 0.24.2

  escalade@3.2.0: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@2.0.0: {}

  escape-string-regexp@4.0.0: {}

  eslint-config-prettier@9.1.0(eslint@9.16.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.16.0(jiti@2.4.2)

  eslint-plugin-prettier@5.4.1(eslint-config-prettier@9.1.0(eslint@9.16.0(jiti@2.4.2)))(eslint@9.16.0(jiti@2.4.2))(prettier@3.5.3):
    dependencies:
      eslint: 9.16.0(jiti@2.4.2)
      prettier: 3.5.3
      prettier-linter-helpers: 1.0.0
      synckit: 0.11.8
    optionalDependencies:
      eslint-config-prettier: 9.1.0(eslint@9.16.0(jiti@2.4.2))

  eslint-plugin-react@7.37.5(eslint@9.16.0(jiti@2.4.2)):
    dependencies:
      array-includes: 3.1.9
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 9.16.0(jiti@2.4.2)
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-plugin-simple-import-sort@12.1.1(eslint@9.16.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.16.0(jiti@2.4.2)

  eslint-plugin-tailwindcss@3.18.0(tailwindcss@3.4.17):
    dependencies:
      fast-glob: 3.3.3
      postcss: 8.5.0
      tailwindcss: 3.4.17

  eslint-scope@8.4.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.1: {}

  eslint@9.16.0(jiti@2.4.2):
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.16.0(jiti@2.4.2))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.19.2
      '@eslint/core': 0.9.1
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.16.0
      '@eslint/plugin-kit': 0.2.8
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.3
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 2.4.2
    transitivePeerDependencies:
      - supports-color

  espree@10.4.0:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 4.2.1

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  esutils@2.0.3: {}

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0

  expect@29.7.0:
    dependencies:
      '@jest/expect-utils': 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  extract-files@13.0.0:
    dependencies:
      is-plain-obj: 4.1.0

  fast-decode-uri-component@1.0.1: {}

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-querystring@1.1.2:
    dependencies:
      fast-decode-uri-component: 1.0.1

  fast-shallow-equal@1.0.0: {}

  fast-uri@3.0.6: {}

  fast-url-parser@1.1.3:
    dependencies:
      punycode: 1.4.1

  fastest-stable-stringify@2.0.2: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fb-watchman@2.0.2:
    dependencies:
      bser: 2.1.1

  fbjs-css-vars@1.0.2: {}

  fbjs@3.0.5:
    dependencies:
      cross-fetch: 3.2.0
      fbjs-css-vars: 1.0.2
      loose-envify: 1.4.0
      object-assign: 4.1.1
      promise: 7.3.1
      setimmediate: 1.0.5
      ua-parser-js: 1.0.40
    transitivePeerDependencies:
      - encoding

  fetch-blob@3.2.0:
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 3.3.3

  fflate@0.4.8: {}

  figures@3.2.0:
    dependencies:
      escape-string-regexp: 1.0.5

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  find-up@7.0.0:
    dependencies:
      locate-path: 7.2.0
      path-exists: 5.0.0
      unicorn-magic: 0.1.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flat-cache@6.1.9:
    dependencies:
      cacheable: 1.9.0
      flatted: 3.3.3
      hookified: 1.9.1

  flatted@3.3.3: {}

  follow-redirects@1.15.9: {}

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  formdata-polyfill@4.0.10:
    dependencies:
      fetch-blob: 3.2.0

  fraction.js@4.3.7: {}

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-nonce@1.0.1: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@8.0.1: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  git-raw-commits@4.0.0:
    dependencies:
      dargs: 8.1.0
      meow: 12.1.1
      split2: 4.2.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@9.3.5:
    dependencies:
      fs.realpath: 1.0.0
      minimatch: 8.0.4
      minipass: 4.2.8
      path-scurry: 1.11.1

  global-directory@4.0.1:
    dependencies:
      ini: 4.1.1

  globals@11.12.0: {}

  globals@14.0.0: {}

  globals@15.15.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  globrex@0.1.2: {}

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  graphql-config@5.1.5(@types/node@20.11.30)(graphql@16.8.1)(typescript@5.4.5):
    dependencies:
      '@graphql-tools/graphql-file-loader': 8.0.20(graphql@16.8.1)
      '@graphql-tools/json-file-loader': 8.0.18(graphql@16.8.1)
      '@graphql-tools/load': 8.1.0(graphql@16.8.1)
      '@graphql-tools/merge': 9.0.24(graphql@16.8.1)
      '@graphql-tools/url-loader': 8.0.31(@types/node@20.11.30)(graphql@16.8.1)
      '@graphql-tools/utils': 10.8.6(graphql@16.8.1)
      cosmiconfig: 8.3.6(typescript@5.4.5)
      graphql: 16.8.1
      jiti: 2.4.2
      minimatch: 9.0.5
      string-env-interpolation: 1.0.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - '@fastify/websocket'
      - '@types/node'
      - bufferutil
      - crossws
      - typescript
      - uWebSockets.js
      - utf-8-validate

  graphql-request@6.1.0(graphql@16.8.1):
    dependencies:
      '@graphql-typed-document-node/core': 3.2.0(graphql@16.8.1)
      cross-fetch: 3.2.0
      graphql: 16.8.1
    transitivePeerDependencies:
      - encoding

  graphql-tag@2.12.6(graphql@16.8.1):
    dependencies:
      graphql: 16.8.1
      tslib: 2.8.1

  graphql-ws@6.0.5(graphql@16.8.1)(ws@8.18.2):
    dependencies:
      graphql: 16.8.1
    optionalDependencies:
      ws: 8.18.2

  graphql@16.8.1: {}

  has-bigints@1.1.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  header-case@2.0.4:
    dependencies:
      capital-case: 1.0.4
      tslib: 2.6.3

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  hookified@1.9.1: {}

  html-parse-stringify@3.0.1:
    dependencies:
      void-elements: 3.1.0

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  human-signals@5.0.0: {}

  hyphenate-style-name@1.1.0: {}

  i18next-browser-languagedetector@7.2.0:
    dependencies:
      '@babel/runtime': 7.27.6

  i18next-locize-backend@6.4.1:
    dependencies:
      cross-fetch: 4.0.0
    transitivePeerDependencies:
      - encoding

  i18next@23.10.1:
    dependencies:
      '@babel/runtime': 7.27.6

  i@0.3.7: {}

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  ieee754@1.2.1: {}

  ignore@5.3.2: {}

  ignore@7.0.5: {}

  immutable@3.7.6: {}

  immutable@4.3.7: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-from@4.0.0: {}

  import-meta-resolve@4.1.0: {}

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@4.1.1: {}

  inline-style-prefixer@7.0.1:
    dependencies:
      css-in-js-utils: 3.1.0

  inquirer@8.2.6:
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      ora: 5.4.1
      run-async: 2.4.1
      rxjs: 7.8.2
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
      wrap-ansi: 6.2.0

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  invariant@2.2.4:
    dependencies:
      loose-envify: 1.4.0

  is-absolute@1.0.0:
    dependencies:
      is-relative: 1.0.0
      is-windows: 1.0.2

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-arrayish@0.2.1: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-fullwidth-code-point@3.0.0: {}

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-interactive@1.0.0: {}

  is-lower-case@2.0.2:
    dependencies:
      tslib: 2.6.3

  is-map@2.0.3: {}

  is-negative-zero@2.0.3: {}

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-obj@2.0.0: {}

  is-plain-obj@4.1.0: {}

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-relative@1.0.0:
    dependencies:
      is-unc-path: 1.0.0

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.4

  is-stream@3.0.0: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-text-path@2.0.0:
    dependencies:
      text-extensions: 2.4.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.19

  is-unc-path@1.0.0:
    dependencies:
      unc-path-regex: 0.1.2

  is-unicode-supported@0.1.0: {}

  is-upper-case@2.0.2:
    dependencies:
      tslib: 2.6.3

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-windows@1.0.2: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isomorphic-ws@5.0.0(ws@8.18.2):
    dependencies:
      ws: 8.18.2

  iterator.prototype@1.1.5:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jest-diff@29.7.0:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-get-type@29.6.3: {}

  jest-matcher-utils@29.7.0:
    dependencies:
      chalk: 4.1.2
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-message-util@29.7.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      '@jest/types': 29.6.3
      '@types/stack-utils': 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-util@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 20.11.30
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jiti@1.21.7: {}

  jiti@2.4.2: {}

  jose@5.10.0: {}

  js-cookie@2.2.1: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-to-pretty-yaml@1.2.2:
    dependencies:
      remedial: 1.0.8
      remove-trailing-spaces: 1.0.9

  json5@2.2.3: {}

  jsonparse@1.3.1: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.9
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  keyv@5.3.3:
    dependencies:
      '@keyv/serialize': 1.0.3

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lilconfig@3.1.3: {}

  lines-and-columns@1.2.4: {}

  listr2@4.0.5:
    dependencies:
      cli-truncate: 2.1.0
      colorette: 2.0.20
      log-update: 4.0.0
      p-map: 4.0.0
      rfdc: 1.4.1
      rxjs: 7.8.2
      through: 2.3.8
      wrap-ansi: 7.0.0

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  locate-path@7.2.0:
    dependencies:
      p-locate: 6.0.0

  lodash.camelcase@4.3.0: {}

  lodash.isplainobject@4.0.6: {}

  lodash.kebabcase@4.1.1: {}

  lodash.merge@4.6.2: {}

  lodash.mergewith@4.6.2: {}

  lodash.snakecase@4.1.1: {}

  lodash.sortby@4.7.0: {}

  lodash.startcase@4.4.0: {}

  lodash.uniq@4.5.0: {}

  lodash.upperfirst@4.3.1: {}

  lodash@4.17.21: {}

  log-symbols@4.1.0:
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0

  log-update@4.0.0:
    dependencies:
      ansi-escapes: 4.3.2
      cli-cursor: 3.1.0
      slice-ansi: 4.0.0
      wrap-ansi: 6.2.0

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lower-case-first@2.0.2:
    dependencies:
      tslib: 2.6.3

  lower-case@2.0.2:
    dependencies:
      tslib: 2.6.3

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lucide-react@0.474.0(react@18.2.0):
    dependencies:
      react: 18.2.0

  magic-string@0.30.8:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  map-cache@0.2.2: {}

  math-intrinsics@1.1.0: {}

  mdn-data@2.0.14: {}

  meow@12.1.1: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  meros@1.3.0(@types/node@20.11.30):
    optionalDependencies:
      '@types/node': 20.11.30

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mimic-fn@2.1.0: {}

  mimic-fn@4.0.0: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@8.0.4:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@4.2.8: {}

  minipass@7.1.2: {}

  ms@2.1.3: {}

  mute-stream@0.0.8: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nano-css@5.6.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0
      css-tree: 1.1.3
      csstype: 3.1.3
      fastest-stable-stringify: 2.0.2
      inline-style-prefixer: 7.0.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      rtl-css-js: 1.16.1
      stacktrace-js: 2.0.2
      stylis: 4.3.6

  nanoid@3.3.11: {}

  natural-compare@1.4.0: {}

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.6.3

  node-domexception@1.0.0: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  node-fetch@3.3.2:
    dependencies:
      data-uri-to-buffer: 4.0.1
      fetch-blob: 3.2.0
      formdata-polyfill: 4.0.10

  node-int64@0.4.0: {}

  node-releases@2.0.19: {}

  normalize-path@2.1.1:
    dependencies:
      remove-trailing-separator: 1.1.0

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  nullthrows@1.1.1: {}

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.13.4: {}

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  optimism@0.18.1:
    dependencies:
      '@wry/caches': 1.0.1
      '@wry/context': 0.7.4
      '@wry/trie': 0.5.0
      tslib: 2.8.1

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  ora@5.4.1:
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.9.2
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1

  os-tmpdir@1.0.2: {}

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-limit@4.0.0:
    dependencies:
      yocto-queue: 1.2.1

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-locate@6.0.0:
    dependencies:
      p-limit: 4.0.0

  p-map@4.0.0:
    dependencies:
      aggregate-error: 3.1.0

  p-try@2.2.0: {}

  package-json-from-dist@1.0.1: {}

  param-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.6.3

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-filepath@1.0.2:
    dependencies:
      is-absolute: 1.0.0
      map-cache: 0.2.2
      path-root: 0.1.1

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  pascal-case@3.1.2:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3

  path-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.6.3

  path-exists@4.0.0: {}

  path-exists@5.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-parse@1.0.7: {}

  path-root-regex@0.1.2: {}

  path-root@0.1.1:
    dependencies:
      path-root-regex: 0.1.2

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-type@4.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pify@2.3.0: {}

  pirates@4.0.7: {}

  possible-typed-array-names@1.1.0: {}

  postcss-import@15.1.0(postcss@8.5.0):
    dependencies:
      postcss: 8.5.0
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.10

  postcss-js@4.0.1(postcss@8.5.0):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.5.0

  postcss-load-config@4.0.2(postcss@8.5.0):
    dependencies:
      lilconfig: 3.1.3
      yaml: 2.8.0
    optionalDependencies:
      postcss: 8.5.0

  postcss-nested@6.2.0(postcss@8.5.0):
    dependencies:
      postcss: 8.5.0
      postcss-selector-parser: 6.1.2

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.5.0:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  posthog-js@1.249.3:
    dependencies:
      core-js: 3.43.0
      fflate: 0.4.8
      preact: 10.26.8
      web-vitals: 4.2.4

  preact@10.26.8: {}

  prelude-ls@1.2.1: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@3.5.3: {}

  pretty-format@29.7.0:
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.3.1

  progress@2.0.3: {}

  promise@7.3.1:
    dependencies:
      asap: 2.0.6

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-from-env@1.1.0: {}

  punycode@1.4.1: {}

  punycode@2.3.1: {}

  pvtsutils@1.3.6:
    dependencies:
      tslib: 2.8.1

  pvutils@1.1.3: {}

  queue-microtask@1.2.3: {}

  react-dom@18.2.0(react@18.2.0):
    dependencies:
      loose-envify: 1.4.0
      react: 18.2.0
      scheduler: 0.23.2

  react-fast-compare@3.2.2: {}

  react-helmet-async@2.0.5(react@18.2.0):
    dependencies:
      invariant: 2.2.4
      react: 18.2.0
      react-fast-compare: 3.2.2
      shallowequal: 1.1.0

  react-hook-form@7.51.1(react@18.2.0):
    dependencies:
      react: 18.2.0

  react-i18next@14.1.0(i18next@23.10.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.27.6
      html-parse-stringify: 3.0.1
      i18next: 23.10.1
      react: 18.2.0
    optionalDependencies:
      react-dom: 18.2.0(react@18.2.0)

  react-is@16.13.1: {}

  react-is@18.3.1: {}

  react-loading-skeleton@3.4.0(react@18.2.0):
    dependencies:
      react: 18.2.0

  react-number-format@5.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  react-popper-tooltip@4.4.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.27.6
      '@popperjs/core': 2.11.8
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-popper: 2.3.0(@popperjs/core@2.11.8)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)

  react-popper@2.3.0(@popperjs/core@2.11.8)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@popperjs/core': 2.11.8
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-fast-compare: 3.2.2
      warning: 4.0.3

  react-remove-scroll-bar@2.3.8(@types/react@18.2.70)(react@18.2.0):
    dependencies:
      react: 18.2.0
      react-style-singleton: 2.2.3(@types/react@18.2.70)(react@18.2.0)
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 18.2.70

  react-remove-scroll@2.7.1(@types/react@18.2.70)(react@18.2.0):
    dependencies:
      react: 18.2.0
      react-remove-scroll-bar: 2.3.8(@types/react@18.2.70)(react@18.2.0)
      react-style-singleton: 2.2.3(@types/react@18.2.70)(react@18.2.0)
      tslib: 2.8.1
      use-callback-ref: 1.3.3(@types/react@18.2.70)(react@18.2.0)
      use-sidecar: 1.1.3(@types/react@18.2.70)(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.70

  react-router-dom@6.22.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@remix-run/router': 1.15.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-router: 6.22.3(react@18.2.0)

  react-router@6.22.3(react@18.2.0):
    dependencies:
      '@remix-run/router': 1.15.3
      react: 18.2.0

  react-style-singleton@2.2.3(@types/react@18.2.70)(react@18.2.0):
    dependencies:
      get-nonce: 1.0.1
      react: 18.2.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 18.2.70

  react-toastify@10.0.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      clsx: 2.1.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  react-universal-interface@0.6.2(react@18.2.0)(tslib@2.8.1):
    dependencies:
      react: 18.2.0
      tslib: 2.8.1

  react-use@17.5.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@types/js-cookie': 2.2.7
      '@xobotyi/scrollbar-width': 1.9.5
      copy-to-clipboard: 3.3.3
      fast-deep-equal: 3.1.3
      fast-shallow-equal: 1.0.0
      js-cookie: 2.2.1
      nano-css: 5.6.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-universal-interface: 0.6.2(react@18.2.0)(tslib@2.8.1)
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      set-harmonic-interval: 1.0.1
      throttle-debounce: 3.0.1
      ts-easing: 0.2.0
      tslib: 2.8.1

  react@18.2.0:
    dependencies:
      loose-envify: 1.4.0

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  rehackt@0.0.6(@types/react@18.2.70)(react@18.2.0):
    optionalDependencies:
      '@types/react': 18.2.70
      react: 18.2.0

  relay-runtime@12.0.0:
    dependencies:
      '@babel/runtime': 7.27.6
      fbjs: 3.0.5
      invariant: 2.2.4
    transitivePeerDependencies:
      - encoding

  remedial@1.0.8: {}

  remove-trailing-separator@1.1.0: {}

  remove-trailing-spaces@1.0.9: {}

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-main-filename@2.0.0: {}

  resize-observer-polyfill@1.5.1: {}

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  response-iterator@0.2.22: {}

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  reusify@1.1.0: {}

  rfdc@1.4.1: {}

  rollup@4.41.1:
    dependencies:
      '@types/estree': 1.0.7
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.41.1
      '@rollup/rollup-android-arm64': 4.41.1
      '@rollup/rollup-darwin-arm64': 4.41.1
      '@rollup/rollup-darwin-x64': 4.41.1
      '@rollup/rollup-freebsd-arm64': 4.41.1
      '@rollup/rollup-freebsd-x64': 4.41.1
      '@rollup/rollup-linux-arm-gnueabihf': 4.41.1
      '@rollup/rollup-linux-arm-musleabihf': 4.41.1
      '@rollup/rollup-linux-arm64-gnu': 4.41.1
      '@rollup/rollup-linux-arm64-musl': 4.41.1
      '@rollup/rollup-linux-loongarch64-gnu': 4.41.1
      '@rollup/rollup-linux-powerpc64le-gnu': 4.41.1
      '@rollup/rollup-linux-riscv64-gnu': 4.41.1
      '@rollup/rollup-linux-riscv64-musl': 4.41.1
      '@rollup/rollup-linux-s390x-gnu': 4.41.1
      '@rollup/rollup-linux-x64-gnu': 4.41.1
      '@rollup/rollup-linux-x64-musl': 4.41.1
      '@rollup/rollup-win32-arm64-msvc': 4.41.1
      '@rollup/rollup-win32-ia32-msvc': 4.41.1
      '@rollup/rollup-win32-x64-msvc': 4.41.1
      fsevents: 2.3.3

  rtl-css-js@1.16.1:
    dependencies:
      '@babel/runtime': 7.27.6

  run-async@2.4.1: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.2.1: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1

  safer-buffer@2.1.2: {}

  sass@1.72.0:
    dependencies:
      chokidar: 3.6.0
      immutable: 4.3.7
      source-map-js: 1.2.1

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  screenfull@5.2.0: {}

  scuid@1.1.0: {}

  semver@6.3.1: {}

  semver@7.7.2: {}

  sentence-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3
      upper-case-first: 2.0.2

  set-blocking@2.0.0: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-harmonic-interval@1.0.1: {}

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  setimmediate@1.0.5: {}

  shallowequal@1.1.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-quote@1.8.3: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  signedsource@1.0.0: {}

  slash@3.0.0: {}

  slice-ansi@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  slice-ansi@4.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  snake-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1

  sonner@2.0.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  source-map-js@1.2.1: {}

  source-map@0.5.6: {}

  source-map@0.6.1: {}

  split2@4.2.0: {}

  sponge-case@1.0.1:
    dependencies:
      tslib: 2.6.3

  stack-generator@2.0.10:
    dependencies:
      stackframe: 1.3.4

  stack-utils@2.0.6:
    dependencies:
      escape-string-regexp: 2.0.0

  stackframe@1.3.4: {}

  stacktrace-gps@3.1.2:
    dependencies:
      source-map: 0.5.6
      stackframe: 1.3.4

  stacktrace-js@2.0.2:
    dependencies:
      error-stack-parser: 2.1.4
      stack-generator: 2.0.10
      stacktrace-gps: 3.1.2

  stop-iteration-iterator@1.1.0:
    dependencies:
      es-errors: 1.3.0
      internal-slot: 1.1.0

  streamsearch@1.1.0: {}

  string-env-interpolation@1.0.1: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.24.0

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-final-newline@3.0.0: {}

  strip-json-comments@3.1.1: {}

  stylis@4.3.6: {}

  sucrase@3.35.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.7
      ts-interface-checker: 0.1.13

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-parser@2.0.4: {}

  swap-case@2.0.2:
    dependencies:
      tslib: 2.6.3

  symbol-observable@4.0.0: {}

  sync-fetch@0.6.0-2:
    dependencies:
      node-fetch: 3.3.2
      timeout-signal: 2.0.0
      whatwg-mimetype: 4.0.0

  synckit@0.11.8:
    dependencies:
      '@pkgr/core': 0.2.7

  tailwind-merge@2.6.0: {}

  tailwindcss-animate@1.0.7(tailwindcss@3.4.17):
    dependencies:
      tailwindcss: 3.4.17

  tailwindcss@3.4.17:
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.7
      lilconfig: 3.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.1
      postcss: 8.5.0
      postcss-import: 15.1.0(postcss@8.5.0)
      postcss-js: 4.0.1(postcss@8.5.0)
      postcss-load-config: 4.0.2(postcss@8.5.0)
      postcss-nested: 6.2.0(postcss@8.5.0)
      postcss-selector-parser: 6.1.2
      resolve: 1.22.10
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  text-extensions@2.4.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  throttle-debounce@3.0.1: {}

  through@2.3.8: {}

  timeout-signal@2.0.0: {}

  tinyexec@1.0.1: {}

  title-case@3.0.3:
    dependencies:
      tslib: 2.6.3

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toggle-selection@1.0.6: {}

  tr46@0.0.3: {}

  ts-api-utils@2.1.0(typescript@5.4.5):
    dependencies:
      typescript: 5.4.5

  ts-easing@0.2.0: {}

  ts-interface-checker@0.1.13: {}

  ts-invariant@0.10.3:
    dependencies:
      tslib: 2.8.1

  ts-log@2.2.7: {}

  tsconfck@3.1.6(typescript@5.4.5):
    optionalDependencies:
      typescript: 5.4.5

  tslib@2.4.1: {}

  tslib@2.6.3: {}

  tslib@2.8.1: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.21.3: {}

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typescript@5.4.5: {}

  ua-parser-js@1.0.40: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  unc-path-regex@0.1.2: {}

  undici-types@5.26.5: {}

  unicorn-magic@0.1.0: {}

  unixify@1.0.0:
    dependencies:
      normalize-path: 2.1.1

  unplugin@1.0.1:
    dependencies:
      acorn: 8.14.1
      chokidar: 3.6.0
      webpack-sources: 3.3.2
      webpack-virtual-modules: 0.5.0

  update-browserslist-db@1.1.3(browserslist@4.25.0):
    dependencies:
      browserslist: 4.25.0
      escalade: 3.2.0
      picocolors: 1.1.1

  upper-case-first@2.0.2:
    dependencies:
      tslib: 2.6.3

  upper-case@2.0.2:
    dependencies:
      tslib: 2.6.3

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  urlpattern-polyfill@10.1.0: {}

  urlpattern-polyfill@8.0.2: {}

  use-callback-ref@1.3.3(@types/react@18.2.70)(react@18.2.0):
    dependencies:
      react: 18.2.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 18.2.70

  use-sidecar@1.1.3(@types/react@18.2.70)(react@18.2.0):
    dependencies:
      detect-node-es: 1.1.0
      react: 18.2.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 18.2.70

  util-deprecate@1.0.2: {}

  uuid@9.0.1: {}

  vite-plugin-svgr@4.2.0(rollup@4.41.1)(typescript@5.4.5)(vite@6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0)):
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.41.1)
      '@svgr/core': 8.1.0(typescript@5.4.5)
      '@svgr/plugin-jsx': 8.1.0(@svgr/core@8.1.0(typescript@5.4.5))
      vite: 6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0)
    transitivePeerDependencies:
      - rollup
      - supports-color
      - typescript

  vite-plugin-webfont-dl@3.10.4(vite@6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0)):
    dependencies:
      axios: 1.9.0
      clean-css: 5.3.3
      flat-cache: 6.1.9
      picocolors: 1.1.1
      vite: 6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0)
    transitivePeerDependencies:
      - debug

  vite-tsconfig-paths@4.3.2(typescript@5.4.5)(vite@6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0)):
    dependencies:
      debug: 4.4.1
      globrex: 0.1.2
      tsconfck: 3.1.6(typescript@5.4.5)
    optionalDependencies:
      vite: 6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0)
    transitivePeerDependencies:
      - supports-color
      - typescript

  vite@6.0.11(@types/node@20.11.30)(jiti@2.4.2)(sass@1.72.0)(yaml@2.8.0):
    dependencies:
      esbuild: 0.24.2
      postcss: 8.5.0
      rollup: 4.41.1
    optionalDependencies:
      '@types/node': 20.11.30
      fsevents: 2.3.3
      jiti: 2.4.2
      sass: 1.72.0
      yaml: 2.8.0

  void-elements@3.1.0: {}

  warning@4.0.3:
    dependencies:
      loose-envify: 1.4.0

  wcwidth@1.0.1:
    dependencies:
      defaults: 1.0.4

  web-streams-polyfill@3.3.3: {}

  web-vitals@4.2.4: {}

  webcrypto-core@1.8.1:
    dependencies:
      '@peculiar/asn1-schema': 2.3.15
      '@peculiar/json-schema': 1.1.12
      asn1js: 3.0.6
      pvtsutils: 1.3.6
      tslib: 2.8.1

  webidl-conversions@3.0.1: {}

  webpack-sources@3.3.2: {}

  webpack-virtual-modules@0.5.0: {}

  whatwg-mimetype@4.0.0: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-module@2.0.1: {}

  which-typed-array@1.1.19:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  ws@8.18.2: {}

  y18n@4.0.3: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yaml-ast-parser@0.0.43: {}

  yaml@2.8.0: {}

  yargs-parser@18.1.3:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs-parser@21.1.1: {}

  yargs@15.4.1:
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}

  yocto-queue@1.2.1: {}

  zen-observable-ts@1.2.5:
    dependencies:
      zen-observable: 0.8.15

  zen-observable@0.8.15: {}

  zod@3.24.1: {}
